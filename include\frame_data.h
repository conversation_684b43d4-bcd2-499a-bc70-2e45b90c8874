#pragma once

#include <vector>
#include <chrono>
#include <memory>
#include <atomic>

/**
 * @brief 捕获帧数据结构
 * 
 * 存储从屏幕捕获的原始帧数据
 */
struct CaptureFrame
{
    std::vector<uint8_t> data;                              // 帧数据 (BGRA格式)
    int width;                                              // 帧宽度
    int height;                                             // 帧高度
    std::chrono::steady_clock::time_point timestamp;        // 捕获时间戳
    uint64_t frame_number;                                  // 帧序号
    size_t data_size;                                       // 数据大小
    
    /**
     * @brief 默认构造函数
     */
    CaptureFrame()
        : width(0), height(0), frame_number(0), data_size(0)
    {
        timestamp = std::chrono::steady_clock::now();
    }

    /**
     * @brief 构造函数
     * @param w 宽度
     * @param h 高度
     * @param frame_num 帧序号
     */
    CaptureFrame(int w, int h, uint64_t frame_num)
        : width(w), height(h), frame_number(frame_num)
    {
        data_size = w * h * 4; // BGRA格式，每像素4字节
        data.resize(data_size);
        timestamp = std::chrono::steady_clock::now();
    }

    /**
     * @brief 移动构造函数
     */
    CaptureFrame(CaptureFrame&& other) noexcept
        : data(std::move(other.data))
        , width(other.width)
        , height(other.height)
        , timestamp(other.timestamp)
        , frame_number(other.frame_number)
        , data_size(other.data_size)
    {
        other.width = 0;
        other.height = 0;
        other.frame_number = 0;
        other.data_size = 0;
    }

    /**
     * @brief 移动赋值操作符
     */
    CaptureFrame& operator=(CaptureFrame&& other) noexcept
    {
        if (this != &other) {
            data = std::move(other.data);
            width = other.width;
            height = other.height;
            timestamp = other.timestamp;
            frame_number = other.frame_number;
            data_size = other.data_size;
            
            other.width = 0;
            other.height = 0;
            other.frame_number = 0;
            other.data_size = 0;
        }
        return *this;
    }

    /**
     * @brief 检查帧是否有效
     * @return 是否有效
     */
    bool IsValid() const
    {
        return width > 0 && height > 0 && !data.empty() && data.size() == data_size;
    }

    /**
     * @brief 获取帧的年龄（从捕获到现在的时间）
     * @return 年龄（毫秒）
     */
    std::chrono::milliseconds GetAge() const
    {
        auto now = std::chrono::steady_clock::now();
        return std::chrono::duration_cast<std::chrono::milliseconds>(now - timestamp);
    }

    /**
     * @brief 重置帧数据
     */
    void Reset()
    {
        data.clear();
        width = 0;
        height = 0;
        frame_number = 0;
        data_size = 0;
        timestamp = std::chrono::steady_clock::now();
    }

    /**
     * @brief 调整帧大小
     * @param w 新宽度
     * @param h 新高度
     */
    void Resize(int w, int h)
    {
        width = w;
        height = h;
        data_size = w * h * 4;
        data.resize(data_size);
    }

    // 禁用拷贝构造和拷贝赋值
    CaptureFrame(const CaptureFrame&) = delete;
    CaptureFrame& operator=(const CaptureFrame&) = delete;
};

/**
 * @brief 编码后的数据包结构
 * 
 * 存储编码后的视频数据包
 */
struct EncodedPacket
{
    std::vector<uint8_t> data;                              // 编码后的数据
    int64_t pts;                                            // 显示时间戳
    int64_t dts;                                            // 解码时间戳
    bool is_keyframe;                                       // 是否为关键帧
    std::chrono::steady_clock::time_point timestamp;        // 编码时间戳
    uint64_t frame_number;                                  // 对应的帧序号
    size_t data_size;                                       // 数据大小
    
    /**
     * @brief 默认构造函数
     */
    EncodedPacket()
        : pts(0), dts(0), is_keyframe(false), frame_number(0), data_size(0)
    {
        timestamp = std::chrono::steady_clock::now();
    }

    /**
     * @brief 构造函数
     * @param size 数据大小
     * @param frame_num 帧序号
     * @param keyframe 是否为关键帧
     */
    EncodedPacket(size_t size, uint64_t frame_num, bool keyframe = false)
        : pts(0), dts(0), is_keyframe(keyframe), frame_number(frame_num), data_size(size)
    {
        data.resize(size);
        timestamp = std::chrono::steady_clock::now();
    }

    /**
     * @brief 移动构造函数
     */
    EncodedPacket(EncodedPacket&& other) noexcept
        : data(std::move(other.data))
        , pts(other.pts)
        , dts(other.dts)
        , is_keyframe(other.is_keyframe)
        , timestamp(other.timestamp)
        , frame_number(other.frame_number)
        , data_size(other.data_size)
    {
        other.pts = 0;
        other.dts = 0;
        other.is_keyframe = false;
        other.frame_number = 0;
        other.data_size = 0;
    }

    /**
     * @brief 移动赋值操作符
     */
    EncodedPacket& operator=(EncodedPacket&& other) noexcept
    {
        if (this != &other) {
            data = std::move(other.data);
            pts = other.pts;
            dts = other.dts;
            is_keyframe = other.is_keyframe;
            timestamp = other.timestamp;
            frame_number = other.frame_number;
            data_size = other.data_size;
            
            other.pts = 0;
            other.dts = 0;
            other.is_keyframe = false;
            other.frame_number = 0;
            other.data_size = 0;
        }
        return *this;
    }

    /**
     * @brief 检查数据包是否有效
     * @return 是否有效
     */
    bool IsValid() const
    {
        return !data.empty() && data.size() == data_size;
    }

    /**
     * @brief 获取数据包的年龄（从编码到现在的时间）
     * @return 年龄（毫秒）
     */
    std::chrono::milliseconds GetAge() const
    {
        auto now = std::chrono::steady_clock::now();
        return std::chrono::duration_cast<std::chrono::milliseconds>(now - timestamp);
    }

    /**
     * @brief 重置数据包
     */
    void Reset()
    {
        data.clear();
        pts = 0;
        dts = 0;
        is_keyframe = false;
        frame_number = 0;
        data_size = 0;
        timestamp = std::chrono::steady_clock::now();
    }

    // 禁用拷贝构造和拷贝赋值
    EncodedPacket(const EncodedPacket&) = delete;
    EncodedPacket& operator=(const EncodedPacket&) = delete;
};

/**
 * @brief 帧缓冲池
 * 
 * 管理帧缓冲区，减少内存分配开销
 */
class FrameBufferPool
{
public:
    /**
     * @brief 构造函数
     * @param initial_size 初始池大小
     * @param max_size 最大池大小
     */
    FrameBufferPool(size_t initial_size = 4, size_t max_size = 16)
        : max_pool_size_(max_size), current_pool_size_(0)
    {
        PreAllocate(initial_size);
    }

    /**
     * @brief 析构函数
     */
    ~FrameBufferPool()
    {
        Clear();
    }

    /**
     * @brief 获取一个帧缓冲区
     * @param width 帧宽度
     * @param height 帧高度
     * @param frame_number 帧序号
     * @return 帧缓冲区
     */
    std::unique_ptr<CaptureFrame> GetFrame(int width, int height, uint64_t frame_number)
    {
        std::lock_guard<std::mutex> lock(pool_mutex_);
        
        std::unique_ptr<CaptureFrame> frame;
        
        if (!available_frames_.empty()) {
            frame = std::move(available_frames_.front());
            available_frames_.pop();
            current_pool_size_--;
            
            // 重新初始化帧
            frame->Resize(width, height);
            frame->frame_number = frame_number;
            frame->timestamp = std::chrono::steady_clock::now();
        } else {
            // 池中没有可用帧，创建新的
            frame = std::make_unique<CaptureFrame>(width, height, frame_number);
        }
        
        return frame;
    }

    /**
     * @brief 归还帧缓冲区到池中
     * @param frame 要归还的帧
     */
    void ReturnFrame(std::unique_ptr<CaptureFrame> frame)
    {
        if (!frame) return;
        
        std::lock_guard<std::mutex> lock(pool_mutex_);
        
        if (current_pool_size_ < max_pool_size_) {
            frame->Reset();
            available_frames_.push(std::move(frame));
            current_pool_size_++;
        }
        // 如果池已满，让frame自动销毁
    }

    /**
     * @brief 预分配帧缓冲区
     * @param count 分配数量
     */
    void PreAllocate(size_t count)
    {
        std::lock_guard<std::mutex> lock(pool_mutex_);
        
        for (size_t i = 0; i < count && current_pool_size_ < max_pool_size_; ++i) {
            auto frame = std::make_unique<CaptureFrame>();
            available_frames_.push(std::move(frame));
            current_pool_size_++;
        }
    }

    /**
     * @brief 清空池
     */
    void Clear()
    {
        std::lock_guard<std::mutex> lock(pool_mutex_);
        
        while (!available_frames_.empty()) {
            available_frames_.pop();
        }
        current_pool_size_ = 0;
    }

    /**
     * @brief 获取池中可用帧数量
     * @return 可用帧数量
     */
    size_t GetAvailableCount() const
    {
        std::lock_guard<std::mutex> lock(pool_mutex_);
        return current_pool_size_;
    }

private:
    std::queue<std::unique_ptr<CaptureFrame>> available_frames_;
    mutable std::mutex pool_mutex_;
    size_t max_pool_size_;
    size_t current_pool_size_;

    // 禁用拷贝构造和赋值
    FrameBufferPool(const FrameBufferPool&) = delete;
    FrameBufferPool& operator=(const FrameBufferPool&) = delete;
};
