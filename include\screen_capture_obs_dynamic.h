#pragma once

#include <memory>
#include <vector>
#include <windows.h>
#include <d3d11.h>

// 前向声明
class WinRTCaptureManager;

// OBS WinRT Capture function pointer types
typedef BOOL (*winrt_capture_supported_func)();
typedef BOOL (*winrt_capture_cursor_toggle_supported_func)();
typedef struct winrt_capture *(*winrt_capture_init_monitor_func)(BOOL cursor, HMONITOR monitor, BOOL force_sdr);
typedef void (*winrt_capture_free_func)(struct winrt_capture *capture);
typedef BOOL (*winrt_capture_active_func)(const struct winrt_capture *capture);
typedef void (*winrt_capture_render_func)(struct winrt_capture *capture);
typedef uint32_t (*winrt_capture_width_func)(const struct winrt_capture *capture);
typedef uint32_t (*winrt_capture_height_func)(const struct winrt_capture *capture);
typedef void (*winrt_capture_thread_start_func)();
typedef void (*winrt_capture_thread_stop_func)();

// OBS D3D11 function pointer types
typedef HRESULT (*d3d11_device_create_func)(ID3D11Device **ppDevice, ID3D11DeviceContext **ppImmediateContext);
typedef HRESULT (*d3d11_texture2d_get_desc_func)(ID3D11Texture2D *pTexture, D3D11_TEXTURE2D_DESC *pDesc);
typedef HRESULT (*d3d11_context_map_func)(ID3D11DeviceContext *pContext, ID3D11Resource *pResource, UINT Subresource, D3D11_MAP MapType, UINT MapFlags, D3D11_MAPPED_SUBRESOURCE *pMappedResource);
typedef void (*d3d11_context_unmap_func)(ID3D11DeviceContext *pContext, ID3D11Resource *pResource, UINT Subresource);

class ScreenCaptureOBSDynamic
{
public:
    ScreenCaptureOBSDynamic();
    ~ScreenCaptureOBSDynamic();

    // Initialize screen capture with dynamic loading
    bool Initialize(int x, int y, int width, int height);

    // Capture screen region
    bool CaptureFrame(std::vector<uint8_t> &frame_data, int &width, int &height);

    // Get screen information
    int GetWidth() const { return capture_width_; }
    int GetHeight() const { return capture_height_; }

    // List available monitors
    void ListMonitors();

private:
    // 新的WinRT捕获管理器
    std::unique_ptr<WinRTCaptureManager> winrt_capture_manager_;

    // 捕获模式选择
    enum CaptureMode
    {
        CAPTURE_MODE_NATIVE_WINRT, // 使用原生WinRT API
        CAPTURE_MODE_OBS_WINRT,    // 使用OBS WinRT库 (fallback)
        CAPTURE_MODE_GDI           // 使用GDI API (最后的fallback)
    };
    CaptureMode current_capture_mode_;

    // OBS WinRT相关 (保留作为fallback)
    HMODULE obs_winrt_dll_;
    HMODULE d3d11_dll_;

    // Function pointers
    winrt_capture_supported_func winrt_capture_supported_;
    winrt_capture_init_monitor_func winrt_capture_init_monitor_;
    winrt_capture_free_func winrt_capture_free_;
    winrt_capture_active_func winrt_capture_active_;
    winrt_capture_render_func winrt_capture_render_;
    winrt_capture_width_func winrt_capture_width_;
    winrt_capture_height_func winrt_capture_height_;
    winrt_capture_thread_start_func winrt_capture_thread_start_;
    winrt_capture_thread_stop_func winrt_capture_thread_stop_;

    // D3D11 function pointers
    d3d11_device_create_func d3d11_create_device_;
    d3d11_texture2d_get_desc_func d3d11_texture2d_get_desc_;
    d3d11_context_map_func d3d11_context_map_;
    d3d11_context_unmap_func d3d11_context_unmap_;

    // Capture state
    struct winrt_capture *capture_;
    HMONITOR selected_monitor_;
    ID3D11Device *d3d11_device_;
    ID3D11DeviceContext *d3d11_context_;

    int capture_x_;
    int capture_y_;
    int capture_width_;
    int capture_height_;
    bool initialized_;

    // Helper functions
    bool InitializeNativeWinRT(int x, int y, int width, int height);
    bool InitializeOBSWinRT(int x, int y, int width, int height);
    bool InitializeGDICapture(int x, int y, int width, int height);

    bool LoadOBSLibraries();
    bool LoadFunctionPointers();
    void Cleanup();
    bool InitializeCapture();
    HMONITOR GetPrimaryMonitor();
    HMONITOR GetMonitorAtPosition(int x, int y);
    bool ConvertTextureToBGRA(std::vector<uint8_t> &frame_data, int &width, int &height);

    // 不同模式的捕获方法
    bool CaptureFrameNativeWinRT(std::vector<uint8_t> &frame_data, int &width, int &height);
    bool CaptureFrameOBSWinRT(std::vector<uint8_t> &frame_data, int &width, int &height);
    bool CaptureFrameGDI(std::vector<uint8_t> &frame_data, int &width, int &height);
};