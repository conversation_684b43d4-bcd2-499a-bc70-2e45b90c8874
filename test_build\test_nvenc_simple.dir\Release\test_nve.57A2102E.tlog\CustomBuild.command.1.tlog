^C:\DEV\NUAA\PUSH_VIDEO_STREAM_SERVER\FFMPEG_PUSH\TEST_BUILD\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/dev/nuaa/push_video_stream_server/ffmpeg_push/test_build -BC:/dev/nuaa/push_video_stream_server/ffmpeg_push/test_build --check-stamp-file C:/dev/nuaa/push_video_stream_server/ffmpeg_push/test_build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
