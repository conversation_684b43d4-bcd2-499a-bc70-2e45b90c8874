# 多线程架构优化方案

## 概述

当前项目采用单线程架构，屏幕捕获、编码和推流都在同一线程中执行，这限制了系统的并发性能。本方案将实现多线程架构，分离捕获和编码线程，采用生产者-消费者模式，大幅提升系统性能。

## 当前架构问题

### 单线程瓶颈
```
主线程: 捕获 → 编码 → 推流 → 等待 → 捕获 → ...
```

**问题分析**:
1. **串行执行**: 捕获必须等待编码完成
2. **帧率限制**: 编码时间直接影响捕获帧率
3. **资源浪费**: CPU和GPU资源无法充分利用
4. **延迟增加**: 处理时间累积导致延迟增加

## 多线程架构设计

### 1. 线程架构

```
捕获线程 (Producer)     编码线程 (Consumer)     推流线程 (Network)
     ↓                      ↓                      ↓
  屏幕捕获              → 帧队列 →              视频编码              → 包队列 →              网络推流
     ↑                      ↑                      ↑
  定时器控制              线程安全队列              异步发送
```

### 2. 核心组件

#### 2.1 线程安全帧队列
```cpp
template<typename T>
class ThreadSafeQueue {
private:
    std::queue<T> queue_;
    std::mutex mutex_;
    std::condition_variable condition_;
    size_t max_size_;
    
public:
    bool Push(T&& item, std::chrono::milliseconds timeout = std::chrono::milliseconds(100));
    bool Pop(T& item, std::chrono::milliseconds timeout = std::chrono::milliseconds(100));
    size_t Size() const;
    void Clear();
};
```

#### 2.2 帧数据结构
```cpp
struct CaptureFrame {
    std::vector<uint8_t> data;
    int width;
    int height;
    std::chrono::steady_clock::time_point timestamp;
    uint64_t frame_number;
    
    // 移动构造函数优化
    CaptureFrame(CaptureFrame&& other) noexcept;
    CaptureFrame& operator=(CaptureFrame&& other) noexcept;
};

struct EncodedPacket {
    std::vector<uint8_t> data;
    int64_t pts;
    int64_t dts;
    bool is_keyframe;
    std::chrono::steady_clock::time_point timestamp;
};
```

#### 2.3 捕获线程管理器
```cpp
class CaptureThreadManager {
private:
    std::unique_ptr<ScreenCaptureOBSDynamic> capture_device_;
    ThreadSafeQueue<CaptureFrame> frame_queue_;
    std::thread capture_thread_;
    std::atomic<bool> running_;
    std::atomic<bool> paused_;
    
    // 性能控制
    std::chrono::steady_clock::time_point last_capture_time_;
    std::chrono::microseconds target_frame_interval_;
    
public:
    bool Start(int fps);
    void Stop();
    void Pause();
    void Resume();
    bool GetFrame(CaptureFrame& frame, std::chrono::milliseconds timeout);
    
private:
    void CaptureLoop();
    void WaitForNextFrame();
};
```

#### 2.4 编码线程管理器
```cpp
class EncodingThreadManager {
private:
    std::unique_ptr<HardwareEncoderManager> encoder_manager_;
    ThreadSafeQueue<CaptureFrame>& input_queue_;
    ThreadSafeQueue<EncodedPacket> output_queue_;
    std::thread encoding_thread_;
    std::atomic<bool> running_;
    
    // 编码状态
    AVCodecContext* codec_context_;
    AVFrame* encode_frame_;
    std::unique_ptr<HardwarePixelConverter> pixel_converter_;
    
public:
    bool Start(const std::string& encoder_type, int width, int height, int fps);
    void Stop();
    bool GetPacket(EncodedPacket& packet, std::chrono::milliseconds timeout);
    
private:
    void EncodingLoop();
    bool EncodeFrame(const CaptureFrame& frame, std::vector<EncodedPacket>& packets);
};
```

#### 2.5 推流线程管理器
```cpp
class StreamingThreadManager {
private:
    std::unique_ptr<StreamPusher> stream_pusher_;
    ThreadSafeQueue<EncodedPacket>& packet_queue_;
    std::thread streaming_thread_;
    std::atomic<bool> running_;
    
    // 网络状态
    std::atomic<bool> connected_;
    std::chrono::steady_clock::time_point last_packet_time_;
    
public:
    bool Start(const std::string& url);
    void Stop();
    bool IsConnected() const;
    
private:
    void StreamingLoop();
    bool SendPacket(const EncodedPacket& packet);
};
```

### 3. 主控制器

```cpp
class MultiThreadStreamManager {
private:
    std::unique_ptr<CaptureThreadManager> capture_manager_;
    std::unique_ptr<EncodingThreadManager> encoding_manager_;
    std::unique_ptr<StreamingThreadManager> streaming_manager_;
    
    // 线程间通信队列
    ThreadSafeQueue<CaptureFrame> capture_queue_;
    ThreadSafeQueue<EncodedPacket> encoding_queue_;
    
    // 性能监控
    std::unique_ptr<PerformanceMonitor> performance_monitor_;
    
    // 配置参数
    StreamConfig config_;
    
public:
    bool Initialize(const StreamConfig& config);
    bool Start();
    void Stop();
    void Pause();
    void Resume();
    
    // 动态配置
    bool ChangeEncoder(const std::string& encoder_type);
    bool ChangeQuality(int bitrate, int fps);
    bool ChangeResolution(int width, int height);
    
    // 状态查询
    StreamStatus GetStatus() const;
    PerformanceMetrics GetMetrics() const;
};
```

## 性能优化策略

### 1. 内存优化

#### 零拷贝设计
```cpp
// 使用移动语义避免不必要的内存拷贝
class FrameBuffer {
private:
    std::vector<uint8_t> buffer_;
    
public:
    // 移动构造，避免拷贝
    FrameBuffer(std::vector<uint8_t>&& data) : buffer_(std::move(data)) {}
    
    // 获取数据指针，避免拷贝
    const uint8_t* Data() const { return buffer_.data(); }
    uint8_t* MutableData() { return buffer_.data(); }
    size_t Size() const { return buffer_.size(); }
};
```

#### 内存池管理
```cpp
class FrameBufferPool {
private:
    std::queue<std::unique_ptr<FrameBuffer>> available_buffers_;
    std::mutex pool_mutex_;
    size_t buffer_size_;
    size_t max_pool_size_;
    
public:
    std::unique_ptr<FrameBuffer> GetBuffer();
    void ReturnBuffer(std::unique_ptr<FrameBuffer> buffer);
    void PreAllocate(size_t count);
};
```

### 2. 线程同步优化

#### 无锁队列 (可选高级优化)
```cpp
template<typename T>
class LockFreeQueue {
private:
    struct Node {
        std::atomic<T*> data;
        std::atomic<Node*> next;
    };
    
    std::atomic<Node*> head_;
    std::atomic<Node*> tail_;
    
public:
    bool Push(T&& item);
    bool Pop(T& item);
};
```

#### 条件变量优化
```cpp
class OptimizedCondition {
private:
    std::mutex mutex_;
    std::condition_variable condition_;
    std::atomic<bool> notified_;
    
public:
    template<typename Predicate>
    bool WaitFor(std::chrono::milliseconds timeout, Predicate pred);
    
    void NotifyOne();
    void NotifyAll();
};
```

### 3. 线程亲和性优化

```cpp
class ThreadAffinityManager {
public:
    static bool SetCaptureThreadAffinity();    // 绑定到性能核心
    static bool SetEncodingThreadAffinity();   // 绑定到独立核心
    static bool SetStreamingThreadAffinity();  // 绑定到网络优化核心
    
private:
    static std::vector<int> GetPerformanceCores();
    static std::vector<int> GetEfficiencyCores();
};
```

## 实现计划

### 阶段1: 基础多线程框架 (3天)
- [ ] 实现ThreadSafeQueue模板类
- [ ] 实现CaptureFrame和EncodedPacket数据结构
- [ ] 创建基础的线程管理器框架

### 阶段2: 捕获线程实现 (2天)
- [ ] 实现CaptureThreadManager
- [ ] 集成现有的屏幕捕获功能
- [ ] 添加帧率控制和时序管理

### 阶段3: 编码线程实现 (2天)
- [ ] 实现EncodingThreadManager
- [ ] 集成硬件编码器管理器
- [ ] 优化像素格式转换

### 阶段4: 推流线程实现 (2天)
- [ ] 实现StreamingThreadManager
- [ ] 集成现有的推流功能
- [ ] 添加网络状态监控

### 阶段5: 主控制器和优化 (2天)
- [ ] 实现MultiThreadStreamManager
- [ ] 添加性能监控
- [ ] 内存池优化

### 阶段6: 测试和调优 (2天)
- [ ] 全面性能测试
- [ ] 线程安全测试
- [ ] 稳定性测试

## 预期性能提升

### 性能指标对比
| 指标 | 单线程 | 多线程 | 提升 |
|------|--------|--------|------|
| 最大帧率 | 30fps | 60fps+ | 100%+ |
| CPU利用率 | 60% | 85%+ | 40%+ |
| 延迟 | 50-100ms | 20-40ms | 50-60% |
| 稳定性 | 中等 | 高 | 显著提升 |

### 资源利用优化
- **CPU**: 多核心并行处理，提升利用率
- **GPU**: 硬件编码器与捕获并行
- **内存**: 减少拷贝，优化缓存局部性
- **网络**: 异步发送，减少阻塞

## 风险和缓解

### 潜在风险
1. **线程同步复杂性**: 可能引入死锁或竞态条件
2. **内存使用增加**: 多个缓冲队列占用更多内存
3. **调试困难**: 多线程问题难以重现和调试

### 缓解措施
1. **严格的锁顺序**: 避免死锁
2. **队列大小限制**: 控制内存使用
3. **详细日志**: 便于问题诊断
4. **单元测试**: 覆盖关键路径

这个多线程架构将显著提升系统性能，特别是在高分辨率和高帧率场景下，同时保持良好的稳定性和可维护性。
