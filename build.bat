@echo off
setlocal enabledelayedexpansion

echo ========================================
echo 高性能屏幕采集推流系统 - 构建脚本
echo ========================================

echo.
echo [1/3] 创建构建目录...
if not exist "build" mkdir build
cd build

echo.
echo [2/3] 配置CMake...
cmake .. -G "Visual Studio 17 2022" -A x64
if %ERRORLEVEL% neq 0 (
    echo ❌ CMake配置失败
    pause
    exit /b 1
)

echo.
echo [3/3] 编译项目...
cmake --build . --config Release
if %ERRORLEVEL% neq 0 (
    echo ❌ 编译失败
    pause
    exit /b 1
)

echo.
echo ✅ 构建成功!
echo.
echo 可执行文件位置: build\bin\Release\screen_capture_stream.exe
echo.
echo 使用方法:
echo   screen_capture_stream.exe --x 0 --y 0 --width 1024 --height 768 --fps 30 --url rtmp://192.168.3.7:1935/live/livestream
echo.

pause 