cmake_minimum_required(VERSION 3.16)
project(screen_capture_stream)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置vcpkg工具链文件路径
if(DEFINED ENV{VCPKG_ROOT} AND NOT DEFINED CMAKE_TOOLCHAIN_FILE)
    set(CMAKE_TOOLCHAIN_FILE "$ENV{VCPKG_ROOT}/scripts/buildsystems/vcpkg.cmake"
        CACHE STRING "")
endif()

# 查找FFmpeg包
find_package(PkgConfig REQUIRED)
pkg_check_modules(FFMPEG REQUIRED 
    libavcodec
    libavformat
    libavutil
    libswscale
    libswresample
)

# 查找OpenCV包
find_package(OpenCV REQUIRED)

# 包含目录
include_directories(${CMAKE_SOURCE_DIR}/include)
include_directories(${CMAKE_SOURCE_DIR}/deps/libobs-winrt)
include_directories(${FFMPEG_INCLUDE_DIRS})
include_directories(${OpenCV_INCLUDE_DIRS})
link_directories(${FFMPEG_LIBRARY_DIRS})

# 添加可执行文件
add_executable(screen_capture_stream
    src/main_obs_dynamic.cpp
    src/screen_capture_obs_dynamic.cpp
    src/stream_pusher.cpp
    src/winrt_capture_manager.cpp
)

# 链接库
target_link_libraries(screen_capture_stream
    ${FFMPEG_LIBRARIES}
    ${OpenCV_LIBS}
    gdi32
    user32
    kernel32
    d3d11
    dxgi
    dwmapi
    windowsapp
    d3dcompiler
    version  # 用于版本检查
)

# 编译选项
target_compile_options(screen_capture_stream PRIVATE ${FFMPEG_CFLAGS_OTHER})

# 设置输出目录
set_target_properties(screen_capture_stream PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
) 