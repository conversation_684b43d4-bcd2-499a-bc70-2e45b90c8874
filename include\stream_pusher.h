#pragma once

extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/imgutils.h>
#include <libswscale/swscale.h>
#include <libavutil/opt.h>
}

#include <string>
#include <vector>

class StreamPusher {
public:
    StreamPusher();
    ~StreamPusher();
    
    // 初始化推流
    bool Initialize(const std::string& url, int width, int height, int fps = 25);
    
    // 推送帧数据
    bool PushFrame(const std::vector<uint8_t>& frame_data, int width, int height);
    
    // 关闭推流
    void Close();
    
private:
    AVFormatContext* format_context_;
    AVCodecContext* codec_context_;
    AVStream* stream_;
    AVFrame* frame_;
    AVPacket* packet_;
    struct SwsContext* sws_context_;
    
    int frame_width_;
    int frame_height_;
    int frame_rate_;
    int64_t frame_count_;
    
    bool initialized_;
    
    void Cleanup();
    bool InitializeCodec(int width, int height, int fps);
    bool InitializeStream();
}; 