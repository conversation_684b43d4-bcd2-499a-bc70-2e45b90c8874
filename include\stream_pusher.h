#pragma once

extern "C"
{
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/imgutils.h>
#include <libswscale/swscale.h>
#include <libavutil/opt.h>
}

#include <string>
#include <vector>
#include <memory>

// 前向声明
class HardwareEncoderManager;
class HardwarePixelConverter;

class StreamPusher
{
public:
    StreamPusher();
    ~StreamPusher();

    // 初始化推流
    bool Initialize(const std::string &url, int width, int height, int fps = 25);

    // 推送帧数据
    bool PushFrame(const std::vector<uint8_t> &frame_data, int width, int height);

    // 关闭推流
    void Close();

    // 新增硬件编码相关方法

    /**
     * @brief 设置首选编码器类型
     * @param encoder_name 编码器名称 ("auto", "nvenc", "amf", "qsv", "software")
     * @return 是否设置成功
     */
    bool SetPreferredEncoder(const std::string &encoder_name);

    /**
     * @brief 获取当前使用的编码器信息
     * @return 编码器信息字符串
     */
    std::string GetCurrentEncoderInfo() const;

    /**
     * @brief 获取编码性能指标
     * @return 性能指标字符串
     */
    std::string GetEncodingMetrics() const;

    /**
     * @brief 动态切换编码器
     * @param encoder_name 新编码器名称
     * @return 是否切换成功
     */
    bool SwitchEncoder(const std::string &encoder_name);

    /**
     * @brief 检查硬件编码器可用性
     * @return 可用编码器列表
     */
    std::vector<std::string> GetAvailableEncoders() const;

private:
    // FFmpeg组件
    AVFormatContext *format_context_;
    AVCodecContext *codec_context_;
    AVStream *stream_;
    AVFrame *frame_;
    AVPacket *packet_;
    struct SwsContext *sws_context_;

    // 硬件编码组件
    std::unique_ptr<HardwareEncoderManager> encoder_manager_;
    std::unique_ptr<HardwarePixelConverter> pixel_converter_;

    // 流参数
    int frame_width_;
    int frame_height_;
    int frame_rate_;
    int64_t frame_count_;

    // 状态
    bool initialized_;
    std::string preferred_encoder_;

    // 私有方法
    void Cleanup();
    bool InitializeCodec(int width, int height, int fps);
    bool InitializeStream();
    bool InitializeHardwareEncoder(int width, int height, int fps);
    bool FallbackToSoftwareEncoder(int width, int height, int fps);
    std::string EncoderNameToType(const std::string &name) const;
};