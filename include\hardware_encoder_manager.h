#pragma once

extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/opt.h>
#include <libavutil/hwcontext.h>
}

#include <vector>
#include <string>
#include <memory>
#include <chrono>

/**
 * @brief GPU硬件编码器管理器
 * 
 * 支持NVENC、AMF、QSV等硬件编码器的检测、选择和配置
 */
class HardwareEncoderManager
{
public:
    /**
     * @brief 支持的编码器类型
     */
    enum EncoderType {
        ENCODER_SOFTWARE = 0,   // libx264 (软件编码)
        ENCODER_NVENC,          // h264_nvenc (NVIDIA)
        ENCODER_AMF,            // h264_amf (AMD)
        ENCODER_QSV,            // h264_qsv (Intel)
        ENCODER_UNKNOWN
    };

    /**
     * @brief 编码器信息
     */
    struct EncoderInfo {
        EncoderType type;
        std::string name;
        std::string description;
        bool available;
        int priority;           // 优先级 (数字越小优先级越高)
        int max_width;          // 最大支持宽度
        int max_height;         // 最大支持高度
        bool supports_bframes;  // 是否支持B帧
        bool supports_cabac;    // 是否支持CABAC
    };

    /**
     * @brief 编码性能指标
     */
    struct EncodingMetrics {
        EncoderType current_encoder;
        std::string encoder_name;
        double avg_encoding_time_ms;
        double max_encoding_time_ms;
        double min_encoding_time_ms;
        int successful_frames;
        int failed_frames;
        double success_rate;
        std::chrono::steady_clock::time_point start_time;
        
        // 性能统计
        std::vector<double> encoding_times;
        
        void Reset() {
            avg_encoding_time_ms = 0.0;
            max_encoding_time_ms = 0.0;
            min_encoding_time_ms = 999999.0;
            successful_frames = 0;
            failed_frames = 0;
            success_rate = 0.0;
            encoding_times.clear();
            start_time = std::chrono::steady_clock::now();
        }
        
        void UpdateMetrics(double encoding_time) {
            encoding_times.push_back(encoding_time);
            
            // 保持最近1000帧的统计
            if (encoding_times.size() > 1000) {
                encoding_times.erase(encoding_times.begin(), encoding_times.begin() + 500);
            }
            
            // 计算统计值
            if (!encoding_times.empty()) {
                double sum = 0.0;
                max_encoding_time_ms = 0.0;
                min_encoding_time_ms = 999999.0;
                
                for (double time : encoding_times) {
                    sum += time;
                    max_encoding_time_ms = std::max(max_encoding_time_ms, time);
                    min_encoding_time_ms = std::min(min_encoding_time_ms, time);
                }
                
                avg_encoding_time_ms = sum / encoding_times.size();
            }
            
            success_rate = (double)successful_frames / (successful_frames + failed_frames) * 100.0;
        }
    };

public:
    HardwareEncoderManager();
    ~HardwareEncoderManager();

    /**
     * @brief 检测系统中可用的硬件编码器
     * @return 可用编码器列表
     */
    std::vector<EncoderInfo> DetectAvailableEncoders();

    /**
     * @brief 选择最佳编码器
     * @param width 视频宽度
     * @param height 视频高度
     * @param fps 帧率
     * @param prefer_quality 是否优先质量 (否则优先性能)
     * @return 选择的编码器类型
     */
    EncoderType SelectBestEncoder(int width, int height, int fps, bool prefer_quality = false);

    /**
     * @brief 创建编码器上下文
     * @param type 编码器类型
     * @param width 视频宽度
     * @param height 视频高度
     * @param fps 帧率
     * @param bitrate 目标码率 (0表示自动计算)
     * @return 编码器上下文 (需要调用者释放)
     */
    AVCodecContext* CreateEncoderContext(EncoderType type, int width, int height, int fps, int bitrate = 0);

    /**
     * @brief 获取编码器名称
     * @param type 编码器类型
     * @return 编码器名称
     */
    static std::string GetEncoderName(EncoderType type);

    /**
     * @brief 获取编码器描述
     * @param type 编码器类型
     * @return 编码器描述
     */
    static std::string GetEncoderDescription(EncoderType type);

    /**
     * @brief 检查特定编码器是否可用
     * @param type 编码器类型
     * @return 是否可用
     */
    bool IsEncoderAvailable(EncoderType type);

    /**
     * @brief 获取编码性能指标
     * @return 性能指标
     */
    const EncodingMetrics& GetMetrics() const { return metrics_; }

    /**
     * @brief 重置性能指标
     */
    void ResetMetrics() { metrics_.Reset(); }

    /**
     * @brief 更新编码性能指标
     * @param encoding_time 编码时间 (毫秒)
     * @param success 是否成功
     */
    void UpdateMetrics(double encoding_time, bool success);

    /**
     * @brief 设置当前使用的编码器类型 (用于指标统计)
     * @param type 编码器类型
     */
    void SetCurrentEncoder(EncoderType type);

private:
    std::vector<EncoderInfo> available_encoders_;
    EncodingMetrics metrics_;
    bool encoders_detected_;

    // 私有方法
    bool DetectNVENC();
    bool DetectAMF();
    bool DetectQSV();
    bool DetectSoftwareEncoder();

    // 编码器配置方法
    bool ConfigureNVENC(AVCodecContext* ctx, int width, int height, int fps, int bitrate);
    bool ConfigureAMF(AVCodecContext* ctx, int width, int height, int fps, int bitrate);
    bool ConfigureQSV(AVCodecContext* ctx, int width, int height, int fps, int bitrate);
    bool ConfigureSoftware(AVCodecContext* ctx, int width, int height, int fps, int bitrate);

    // 辅助方法
    int CalculateOptimalBitrate(int width, int height, int fps, EncoderType encoder_type);
    bool TestEncoder(const AVCodec* codec, int width, int height);
    AVPixelFormat GetPreferredPixelFormat(EncoderType type);

    // 硬件上下文相关
    AVBufferRef* CreateHardwareContext(EncoderType type);
    bool SetupHardwareContext(AVCodecContext* ctx, EncoderType type);
};

/**
 * @brief 像素格式转换器 (针对硬件编码器优化)
 */
class HardwarePixelConverter
{
public:
    HardwarePixelConverter();
    ~HardwarePixelConverter();

    /**
     * @brief 初始化转换器
     * @param src_width 源宽度
     * @param src_height 源高度
     * @param dst_format 目标像素格式
     * @return 是否成功
     */
    bool Initialize(int src_width, int src_height, AVPixelFormat dst_format);

    /**
     * @brief 转换BGRA到目标格式
     * @param bgra_data BGRA数据
     * @param dst_frame 目标帧
     * @return 是否成功
     */
    bool ConvertBGRAToFrame(const std::vector<uint8_t>& bgra_data, AVFrame* dst_frame);

    /**
     * @brief 清理资源
     */
    void Cleanup();

private:
    struct SwsContext* sws_context_;
    int src_width_;
    int src_height_;
    AVPixelFormat dst_format_;
    bool initialized_;

    // 临时缓冲区
    std::vector<uint8_t> temp_buffer_;
};

/**
 * @brief 硬件编码器辅助函数
 */
namespace HardwareEncoderHelper
{
    /**
     * @brief 检查系统是否支持硬件编码
     * @return 是否支持
     */
    bool IsHardwareEncodingSupported();

    /**
     * @brief 获取GPU信息
     * @return GPU信息字符串
     */
    std::string GetGPUInfo();

    /**
     * @brief 检查NVIDIA驱动版本
     * @return 驱动版本字符串
     */
    std::string GetNVIDIADriverVersion();

    /**
     * @brief 检查AMD驱动版本
     * @return 驱动版本字符串
     */
    std::string GetAMDDriverVersion();

    /**
     * @brief 检查Intel驱动版本
     * @return 驱动版本字符串
     */
    std::string GetIntelDriverVersion();

    /**
     * @brief 获取推荐的编码器设置
     * @param width 视频宽度
     * @param height 视频高度
     * @param fps 帧率
     * @param encoder_type 编码器类型
     * @return 推荐设置的描述
     */
    std::string GetRecommendedSettings(int width, int height, int fps, 
                                     HardwareEncoderManager::EncoderType encoder_type);
}
