# WinRT高性能屏幕捕获优化方案

## 问题分析

### 当前实现的问题
1. **虚假的WinRT实现**: 虽然加载了OBS的WinRT库，但实际的像素数据获取仍使用GDI API
2. **性能瓶颈**: GDI API (`GetDC`, `BitBlt`) 性能远低于真正的WinRT捕获
3. **兼容性问题**: 无法捕获受保护的内容（如某些游戏、DRM内容）
4. **现代显示技术支持不足**: 无法充分利用Windows 10/11的现代显示技术

### 当前代码问题点
```cpp
// 在 ConvertTextureToBGRA 方法中使用了GDI API
HDC screen_dc = GetDC(nullptr);           // ❌ 使用GDI
HDC mem_dc = CreateCompatibleDC(screen_dc); // ❌ 使用GDI  
BitBlt(mem_dc, 0, 0, width, height, screen_dc, capture_x_, capture_y_, SRCCOPY); // ❌ 使用GDI
```

## 真正的WinRT实现方案

### 1. 技术架构

#### WinRT Graphics Capture API
- **Windows.Graphics.Capture**: Windows 10 1903+ 的现代屏幕捕获API
- **Direct3D 11集成**: 直接获取GPU纹理，无需CPU拷贝
- **硬件加速**: 充分利用GPU进行像素格式转换
- **受保护内容支持**: 可以捕获更多类型的内容

#### 实现层次
```
应用层 (C++)
    ↓
Windows.Graphics.Capture (WinRT)
    ↓  
Direct3D 11 Texture
    ↓
GPU Memory → CPU Memory (高效转换)
```

### 2. 核心实现组件

#### 2.1 WinRT捕获管理器
```cpp
class WinRTCaptureManager {
private:
    winrt::Windows::Graphics::Capture::GraphicsCaptureSession capture_session_;
    winrt::Windows::Graphics::Capture::Direct3D11CaptureFramePool frame_pool_;
    winrt::Windows::Graphics::DirectX::Direct3D11::IDirect3DDevice winrt_device_;
    
public:
    bool Initialize(HWND target_window);
    bool CaptureFrame(std::vector<uint8_t>& frame_data);
    void Cleanup();
};
```

#### 2.2 D3D11纹理处理器
```cpp
class D3D11TextureProcessor {
private:
    ID3D11Device* d3d11_device_;
    ID3D11DeviceContext* d3d11_context_;
    ID3D11Texture2D* staging_texture_;
    
public:
    bool ConvertTextureToMemory(ID3D11Texture2D* source_texture, 
                               std::vector<uint8_t>& output_data);
    bool CreateStagingTexture(int width, int height);
};
```

### 3. 详细实现步骤

#### 步骤1: 初始化WinRT捕获
```cpp
bool WinRTCaptureManager::Initialize(HWND target_window) {
    // 1. 创建Direct3D设备
    auto d3d_device = CreateD3DDevice();
    winrt_device_ = CreateWinRTDevice(d3d_device);
    
    // 2. 创建捕获项
    auto capture_item = CreateCaptureItemForWindow(target_window);
    
    // 3. 创建帧池
    frame_pool_ = Direct3D11CaptureFramePool::Create(
        winrt_device_,
        DirectXPixelFormat::B8G8R8A8UIntNormalized,
        2, // 缓冲帧数
        capture_item.Size()
    );
    
    // 4. 创建捕获会话
    capture_session_ = frame_pool_.CreateCaptureSession(capture_item);
    capture_session_.StartCapture();
    
    return true;
}
```

#### 步骤2: 高效帧捕获
```cpp
bool WinRTCaptureManager::CaptureFrame(std::vector<uint8_t>& frame_data) {
    // 1. 获取最新帧
    auto frame = frame_pool_.TryGetNextFrame();
    if (!frame) return false;
    
    // 2. 获取D3D11纹理
    auto access = frame.Surface().as<Windows::Graphics::DirectX::Direct3D11::IDirect3DDxgiInterfaceAccess>();
    com_ptr<ID3D11Texture2D> texture;
    access->GetInterface(IID_PPV_ARGS(&texture));
    
    // 3. 转换为内存数据
    return texture_processor_.ConvertTextureToMemory(texture.get(), frame_data);
}
```

#### 步骤3: 纹理到内存转换
```cpp
bool D3D11TextureProcessor::ConvertTextureToMemory(ID3D11Texture2D* source_texture, 
                                                   std::vector<uint8_t>& output_data) {
    // 1. 复制到可读取的staging纹理
    d3d11_context_->CopyResource(staging_texture_, source_texture);
    
    // 2. 映射内存
    D3D11_MAPPED_SUBRESOURCE mapped_resource;
    HRESULT hr = d3d11_context_->Map(staging_texture_, 0, D3D11_MAP_READ, 0, &mapped_resource);
    if (FAILED(hr)) return false;
    
    // 3. 复制像素数据
    uint8_t* src_data = static_cast<uint8_t*>(mapped_resource.pData);
    size_t row_pitch = mapped_resource.RowPitch;
    
    // 高效内存复制，考虑行对齐
    for (int y = 0; y < height_; ++y) {
        memcpy(output_data.data() + y * width_ * 4, 
               src_data + y * row_pitch, 
               width_ * 4);
    }
    
    // 4. 取消映射
    d3d11_context_->Unmap(staging_texture_, 0);
    return true;
}
```

### 4. 性能优化策略

#### 4.1 内存池管理
```cpp
class FrameBufferPool {
private:
    std::queue<std::vector<uint8_t>> available_buffers_;
    std::mutex buffer_mutex_;
    
public:
    std::vector<uint8_t> GetBuffer(size_t size);
    void ReturnBuffer(std::vector<uint8_t>&& buffer);
};
```

#### 4.2 异步处理
```cpp
class AsyncCaptureProcessor {
private:
    std::thread capture_thread_;
    std::queue<CaptureFrame> frame_queue_;
    std::condition_variable frame_ready_;
    
public:
    void StartAsyncCapture();
    bool GetProcessedFrame(std::vector<uint8_t>& frame_data);
};
```

### 5. 集成到现有代码

#### 5.1 修改ScreenCaptureOBSDynamic类
```cpp
class ScreenCaptureOBSDynamic {
private:
    // 新增WinRT组件
    std::unique_ptr<WinRTCaptureManager> winrt_capture_manager_;
    std::unique_ptr<D3D11TextureProcessor> texture_processor_;
    std::unique_ptr<FrameBufferPool> buffer_pool_;
    
    // 保留OBS兼容性（作为fallback）
    bool use_winrt_capture_;
    
public:
    bool Initialize(int x, int y, int width, int height) override;
    bool CaptureFrame(std::vector<uint8_t>& frame_data, int& width, int& height) override;
};
```

#### 5.2 智能回退机制
```cpp
bool ScreenCaptureOBSDynamic::Initialize(int x, int y, int width, int height) {
    // 1. 尝试使用真正的WinRT捕获
    if (InitializeWinRTCapture(x, y, width, height)) {
        use_winrt_capture_ = true;
        std::cout << "Using native WinRT capture (high performance)" << std::endl;
        return true;
    }
    
    // 2. 回退到OBS WinRT (当前实现)
    if (InitializeOBSCapture(x, y, width, height)) {
        use_winrt_capture_ = false;
        std::cout << "Using OBS WinRT capture (fallback)" << std::endl;
        return true;
    }
    
    return false;
}
```

### 6. 预期性能提升

#### 性能对比
| 方案 | CPU占用 | 内存占用 | 延迟 | 兼容性 |
|------|---------|----------|------|--------|
| 当前GDI | 高 | 中 | 高 | 一般 |
| OBS WinRT | 中 | 中 | 中 | 好 |
| 真正WinRT | 低 | 低 | 低 | 最好 |

#### 预期提升
- **性能提升**: 30-50% CPU占用降低
- **延迟降低**: 5-10ms 延迟减少
- **内存效率**: 20-30% 内存占用优化
- **兼容性**: 支持更多现代显示技术

### 7. 实施计划

#### 阶段1: 基础WinRT实现 (1周)
- [ ] 实现WinRTCaptureManager基础功能
- [ ] 实现D3D11TextureProcessor
- [ ] 基本的纹理到内存转换

#### 阶段2: 性能优化 (1周)  
- [ ] 实现内存池管理
- [ ] 添加异步处理
- [ ] 优化内存复制效率

#### 阶段3: 集成和测试 (1周)
- [ ] 集成到现有代码
- [ ] 实现智能回退机制
- [ ] 全面性能测试

### 8. 风险和缓解

#### 潜在风险
1. **Windows版本兼容性**: WinRT Capture需要Windows 10 1903+
2. **驱动兼容性**: 某些显卡驱动可能有问题
3. **权限问题**: 某些应用可能需要特殊权限

#### 缓解措施
1. **版本检测**: 运行时检测Windows版本
2. **多重回退**: OBS WinRT → GDI → 错误处理
3. **权限处理**: 提供管理员权限选项

这个方案将显著提升屏幕捕获性能，同时保持良好的兼容性。
