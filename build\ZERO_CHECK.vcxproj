﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{CB5C35AC-EC70-3D24-83A1-EE63B6C2AE29}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ZERO_CHECK</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>C:\dev\nuaa\push_video_stream_server\ffmpeg_push\include;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\deps\libobs-winrt;C:\dev\vcpkg\installed\x64-windows\debug\..\include;C:\dev\vcpkg\installed\x64-windows\include\opencv4;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>C:\dev\nuaa\push_video_stream_server\ffmpeg_push\include;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\deps\libobs-winrt;C:\dev\vcpkg\installed\x64-windows\debug\..\include;C:\dev\vcpkg\installed\x64-windows\include\opencv4;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>C:\dev\nuaa\push_video_stream_server\ffmpeg_push\include;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\deps\libobs-winrt;C:\dev\vcpkg\installed\x64-windows\debug\..\include;C:\dev\vcpkg\installed\x64-windows\include\opencv4;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>C:\dev\nuaa\push_video_stream_server\ffmpeg_push\include;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\deps\libobs-winrt;C:\dev\vcpkg\installed\x64-windows\debug\..\include;C:\dev\vcpkg\installed\x64-windows\include\opencv4;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\09965c15b5f1743edb9960c35ad12236\generate.stamp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/dev/nuaa/push_video_stream_server/ffmpeg_push -BC:/dev/nuaa/push_video_stream_server/ffmpeg_push/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/dev/nuaa/push_video_stream_server/ffmpeg_push/build/screen_capture_stream.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPkgConfig.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindTIFF.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\CMakeLists.txt;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeCCompiler.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeSystem.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVConfig-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVModules-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVModules-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVModules.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-generate.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-module.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-options.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\quirc\quirc-config-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\quirc\quirc-config-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\quirc\quirc-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\tiff\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-targets.cmake;C:\dev\vcpkg\scripts\buildsystems\vcpkg.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/dev/nuaa/push_video_stream_server/ffmpeg_push -BC:/dev/nuaa/push_video_stream_server/ffmpeg_push/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/dev/nuaa/push_video_stream_server/ffmpeg_push/build/screen_capture_stream.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPkgConfig.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindTIFF.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\CMakeLists.txt;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeCCompiler.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeSystem.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVConfig-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVModules-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVModules-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVModules.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-generate.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-module.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-options.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\quirc\quirc-config-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\quirc\quirc-config-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\quirc\quirc-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\tiff\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-targets.cmake;C:\dev\vcpkg\scripts\buildsystems\vcpkg.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/dev/nuaa/push_video_stream_server/ffmpeg_push -BC:/dev/nuaa/push_video_stream_server/ffmpeg_push/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/dev/nuaa/push_video_stream_server/ffmpeg_push/build/screen_capture_stream.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPkgConfig.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindTIFF.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\CMakeLists.txt;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeCCompiler.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeSystem.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVConfig-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVModules-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVModules-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVModules.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-generate.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-module.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-options.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\quirc\quirc-config-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\quirc\quirc-config-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\quirc\quirc-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\tiff\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-targets.cmake;C:\dev\vcpkg\scripts\buildsystems\vcpkg.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/dev/nuaa/push_video_stream_server/ffmpeg_push -BC:/dev/nuaa/push_video_stream_server/ffmpeg_push/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/dev/nuaa/push_video_stream_server/ffmpeg_push/build/screen_capture_stream.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPkgConfig.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindTIFF.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\CMakeLists.txt;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeCCompiler.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeSystem.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVConfig-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVModules-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVModules-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVModules.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-generate.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-module.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-options.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\quirc\quirc-config-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\quirc\quirc-config-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\quirc\quirc-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\tiff\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-targets.cmake;C:\dev\vcpkg\scripts\buildsystems\vcpkg.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>