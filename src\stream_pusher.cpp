#include "stream_pusher.h"
#include <iostream>
#include <chrono>
#include <thread>

StreamPusher::StreamPusher()
    : format_context_(nullptr), codec_context_(nullptr), stream_(nullptr), frame_(nullptr), packet_(nullptr), sws_context_(nullptr), frame_width_(0), frame_height_(0), frame_rate_(25), frame_count_(0), initialized_(false)
{
}

StreamPusher::~StreamPusher()
{
    Cleanup();
}

bool StreamPusher::Initialize(const std::string &url, int width, int height, int fps)
{
    Cleanup();

    frame_width_ = width;
    frame_height_ = height;
    frame_rate_ = fps;
    frame_count_ = 0;

    std::cout << "Initializing stream pusher..." << std::endl;
    std::cout << "  URL: " << url << std::endl;
    std::cout << "  Resolution: " << width << "x" << height << std::endl;
    std::cout << "  Frame rate: " << fps << " fps" << std::endl;
    std::cout << "  Using: FFmpeg libx264 encoder + RTMP protocol" << std::endl;

    // Initialize FFmpeg network
    avformat_network_init();

    // Create output format context
    avformat_alloc_output_context2(&format_context_, nullptr, "flv", url.c_str());
    if (!format_context_)
    {
        std::cerr << "Failed to create output format context" << std::endl;
        return false;
    }

    // Initialize codec
    if (!InitializeCodec(width, height, fps))
    {
        return false;
    }

    // Initialize stream
    if (!InitializeStream())
    {
        return false;
    }

    // Open output URL with timeout and retry
    std::cout << "Opening output URL..." << std::endl;
    std::cout << "  Protocol: RTMP" << std::endl;
    std::cout << "  Target: " << url << std::endl;

    // Set network timeout options
    AVDictionary *options = nullptr;
    av_dict_set(&options, "timeout", "5000000", 0); // 5 seconds timeout
    av_dict_set(&options, "reconnect", "1", 0);     // Enable reconnection
    av_dict_set(&options, "reconnect_streamed", "1", 0);
    av_dict_set(&options, "reconnect_delay_max", "2", 0); // Max 2 seconds delay

    int ret = avio_open(&format_context_->pb, url.c_str(), AVIO_FLAG_WRITE);
    if (ret < 0)
    {
        char error_msg[AV_ERROR_MAX_STRING_SIZE];
        av_strerror(ret, error_msg, AV_ERROR_MAX_STRING_SIZE);
        std::cerr << "Failed to open output URL: " << url << std::endl;
        std::cerr << "Error: " << error_msg << " (code: " << ret << ")" << std::endl;
        std::cerr << "Network diagnostics:" << std::endl;
        std::cerr << "  1. Check if RTMP server is running" << std::endl;
        std::cerr << "  2. Verify network connectivity" << std::endl;
        std::cerr << "  3. Check firewall settings" << std::endl;
        std::cerr << "  4. Ensure URL format is correct" << std::endl;
        std::cerr << "  5. Try using localhost for testing" << std::endl;

        if (av_dict_get(options, nullptr, nullptr, AV_DICT_IGNORE_SUFFIX))
        {
            av_dict_free(&options);
        }
        return false;
    }

    if (av_dict_get(options, nullptr, nullptr, AV_DICT_IGNORE_SUFFIX))
    {
        av_dict_free(&options);
    }

    // Write stream header
    std::cout << "Writing stream header..." << std::endl;
    if (avformat_write_header(format_context_, nullptr) < 0)
    {
        std::cerr << "Failed to write stream header" << std::endl;
        return false;
    }

    initialized_ = true;
    std::cout << "Stream pusher initialized successfully" << std::endl;
    std::cout << "  Encoder: libx264 (H.264)" << std::endl;
    std::cout << "  Format: FLV" << std::endl;
    std::cout << "  Protocol: RTMP" << std::endl;
    return true;
}

bool StreamPusher::InitializeCodec(int width, int height, int fps)
{
    // Find H.264 encoder
    const AVCodec *codec = avcodec_find_encoder(AV_CODEC_ID_H264);
    if (!codec)
    {
        std::cerr << "Failed to find H.264 encoder" << std::endl;
        return false;
    }

    // Create codec context
    codec_context_ = avcodec_alloc_context3(codec);
    if (!codec_context_)
    {
        std::cerr << "Failed to allocate codec context" << std::endl;
        return false;
    }

    // Set encoding parameters - 高质量配置
    codec_context_->width = width;
    codec_context_->height = height;
    codec_context_->time_base = AVRational{1, fps};
    codec_context_->framerate = AVRational{fps, 1};
    codec_context_->pix_fmt = AV_PIX_FMT_YUV420P;

    // 根据分辨率动态计算码率
    int pixel_count = width * height;
    int target_bitrate;
    if (pixel_count <= 786432) // 1024x768
    {
        target_bitrate = 2000000; // 2Mbps for 1024x768
    }
    else if (pixel_count <= 2073600) // 1920x1080
    {
        target_bitrate = 4000000; // 4Mbps for 1080p
    }
    else // 4K
    {
        target_bitrate = 8000000; // 8Mbps for 4K
    }

    codec_context_->bit_rate = target_bitrate;
    codec_context_->gop_size = fps * 2; // 2秒GOP
    codec_context_->max_b_frames = 0;   // 禁用B帧以避免时间戳问题

    // 高质量H.264编码参数
    codec_context_->profile = FF_PROFILE_H264_HIGH; // 使用High Profile
    codec_context_->level = 41;                     // 4.1 level for better quality
    codec_context_->flags |= AV_CODEC_FLAG_GLOBAL_HEADER;
    codec_context_->flags2 |= AV_CODEC_FLAG2_FAST;

    // 优化关键帧设置
    codec_context_->keyint_min = fps; // 1秒最小关键帧间隔
    codec_context_->i_quant_factor = 0.71f;
    codec_context_->qcompress = 0.6f;
    codec_context_->qblur = 0.5f;
    codec_context_->max_qdiff = 4;

    // 高质量色彩空间设置
    codec_context_->colorspace = AVCOL_SPC_BT709;
    codec_context_->color_range = AVCOL_RANGE_MPEG; // 使用MPEG范围
    codec_context_->color_primaries = AVCOL_PRI_BT709;
    codec_context_->color_trc = AVCOL_TRC_BT709;

    // 屏幕内容优化编码预设 - 参考OBS Studio最佳实践
    av_opt_set(codec_context_->priv_data, "preset", "veryfast", 0);    // 性能平衡最佳选择
    av_opt_set(codec_context_->priv_data, "tune", "zerolatency", 0);   // 低延迟优化，适合实时流
    av_opt_set(codec_context_->priv_data, "profile", "high", 0);
    av_opt_set(codec_context_->priv_data, "level", "4.1", 0);

    // 专为屏幕内容优化的x264参数
    std::string x264opts = "keyint=" + std::to_string(fps * 2) +
                           ":min-keyint=" + std::to_string(fps) +
                           ":ref=2" +           // 减少参考帧提升性能
                           ":bframes=0" +       // 禁用B帧减少延迟
                           ":cabac=1" +         // 启用CABAC提升压缩效率
                           ":deblock=1:0:0" +   // 去块滤波器
                           ":analyse=0x3:0x3" + // 快速分析模式
                           ":me=hex" +          // 运动估计算法
                           ":subme=2" +         // 亚像素运动估计
                           ":psy=0" +           // 禁用心理视觉优化（屏幕内容不需要）
                           ":mixed-refs=0" +    // 禁用混合参考
                           ":me-range=16" +     // 运动搜索范围
                           ":chroma-me=1" +     // 色度运动估计
                           ":trellis=0" +       // 禁用trellis量化提升速度
                           ":8x8dct=1" +        // 启用8x8DCT
                           ":cqm=0" +           // 禁用自定义量化矩阵
                           ":deadzone-inter=21" + // 量化死区
                           ":deadzone-intra=11" +
                           ":fast-pskip=1" +    // 快速P帧跳过
                           ":chroma-qp-offset=0" + // 色度QP偏移
                           ":threads=0" +       // 自动线程数
                           ":sliced-threads=0" + // 禁用切片线程
                           ":nr=0" +            // 禁用降噪
                           ":decimate=1" +      // 启用帧抽取
                           ":interlaced=0" +    // 非交错
                           ":bluray-compat=0" + // 非蓝光兼容
                           ":constrained-intra=0"; // 非约束帧内预测

    av_opt_set(codec_context_->priv_data, "x264opts", x264opts.c_str(), 0);

    // 优化码率控制 - CBR模式确保稳定推流
    av_opt_set(codec_context_->priv_data, "rc-lookahead", "10", 0);     // 减少前瞻提升速度
    av_opt_set(codec_context_->priv_data, "vbv-bufsize", std::to_string(target_bitrate / 1000).c_str(), 0); // VBV缓冲区等于码率
    av_opt_set(codec_context_->priv_data, "vbv-maxrate", std::to_string(target_bitrate / 1000).c_str(), 0); // 最大码率等于目标码率
    av_opt_set(codec_context_->priv_data, "nal-hrd", "cbr", 0);         // CBR HRD兼容
    av_opt_set(codec_context_->priv_data, "force-cfr", "1", 0);         // 强制恒定帧率

    // Open codec
    if (avcodec_open2(codec_context_, codec, nullptr) < 0)
    {
        std::cerr << "Failed to open codec" << std::endl;
        return false;
    }

    std::cout << "Screen Content Optimized H.264 encoder initialized successfully" << std::endl;
    std::cout << "  Profile: High" << std::endl;
    std::cout << "  Level: 4.1" << std::endl;
    std::cout << "  Preset: veryfast (Performance Optimized)" << std::endl;
    std::cout << "  Tune: zerolatency (Real-time Streaming)" << std::endl;
    std::cout << "  Bitrate: " << codec_context_->bit_rate / 1000 << " kbps" << std::endl;
    std::cout << "  GOP: " << codec_context_->gop_size << " frames (" << (codec_context_->gop_size / fps) << "s)" << std::endl;
    std::cout << "  B-frames: 0 (Low Latency)" << std::endl;
    std::cout << "  VBV Buffer: " << target_bitrate / 1000 << " kbits" << std::endl;

    return true;
}

bool StreamPusher::InitializeStream()
{
    // Create stream
    stream_ = avformat_new_stream(format_context_, nullptr);
    if (!stream_)
    {
        std::cerr << "Failed to create stream" << std::endl;
        return false;
    }

    // Set stream parameters
    stream_->time_base = codec_context_->time_base;

    // 关键：正确设置编解码器参数
    int ret = avcodec_parameters_from_context(stream_->codecpar, codec_context_);
    if (ret < 0)
    {
        std::cerr << "Failed to copy codec parameters to stream" << std::endl;
        return false;
    }

    // 确保流参数正确设置
    stream_->codecpar->codec_type = AVMEDIA_TYPE_VIDEO;
    stream_->codecpar->codec_id = AV_CODEC_ID_H264;
    stream_->codecpar->width = frame_width_;
    stream_->codecpar->height = frame_height_;
    stream_->codecpar->format = AV_PIX_FMT_YUV420P;
    stream_->codecpar->bit_rate = codec_context_->bit_rate;
    stream_->codecpar->profile = FF_PROFILE_H264_HIGH; // 更新为High Profile
    stream_->codecpar->level = 41;                     // 更新为4.1 level

    // Create frame
    frame_ = av_frame_alloc();
    if (!frame_)
    {
        std::cerr << "Failed to allocate frame" << std::endl;
        return false;
    }

    frame_->format = AV_PIX_FMT_YUV420P;
    frame_->width = frame_width_;
    frame_->height = frame_height_;

    if (av_frame_get_buffer(frame_, 32) < 0)
    {
        std::cerr << "Failed to allocate frame buffer" << std::endl;
        return false;
    }

    // Create packet
    packet_ = av_packet_alloc();
    if (!packet_)
    {
        std::cerr << "Failed to allocate packet" << std::endl;
        return false;
    }

    std::cout << "Stream initialized successfully" << std::endl;
    std::cout << "  Stream index: " << stream_->index << std::endl;
    std::cout << "  Time base: " << stream_->time_base.num << "/" << stream_->time_base.den << std::endl;
    std::cout << "  Codec params: " << stream_->codecpar->width << "x" << stream_->codecpar->height << std::endl;

    return true;
}

bool StreamPusher::PushFrame(const std::vector<uint8_t> &frame_data, int width, int height)
{
    if (!initialized_)
    {
        std::cerr << "Stream pusher not initialized" << std::endl;
        return false;
    }

    if (frame_data.empty())
    {
        std::cerr << "Frame data is empty" << std::endl;
        return false;
    }

    // 创建高质量BGRA到YUV转换上下文 - 优化屏幕内容
    if (!sws_context_)
    {
        // 使用更高质量的缩放算法
        int sws_flags = SWS_LANCZOS | // Lanczos算法，适合屏幕内容
                       SWS_FULL_CHR_H_INT | // 全精度色度插值
                       SWS_FULL_CHR_H_INP | // 全精度色度输入
                       SWS_ACCURATE_RND |   // 精确舍入
                       SWS_BITEXACT;        // 位精确

        sws_context_ = sws_getContext(
            width, height, AV_PIX_FMT_BGRA,
            frame_width_, frame_height_, AV_PIX_FMT_YUV420P,
            sws_flags, nullptr, nullptr, nullptr);
            
        if (!sws_context_)
        {
            std::cerr << "Failed to create sws context with optimized flags" << std::endl;
            // 降级到标准算法
            sws_context_ = sws_getContext(
                width, height, AV_PIX_FMT_BGRA,
                frame_width_, frame_height_, AV_PIX_FMT_YUV420P,
                SWS_BILINEAR, nullptr, nullptr, nullptr);
            if (!sws_context_)
            {
                std::cerr << "Failed to create fallback sws context" << std::endl;
                return false;
            }
        }
        
        // 设置YUV色彩空间转换矩阵 - BT.709标准
        const int *coefficients = sws_getCoefficients(SWS_CS_BT709);
        sws_setColorspaceDetails(sws_context_,
                               coefficients, 0,  // 源色彩空间
                               coefficients, 0,  // 目标色彩空间  
                               0, 1 << 16, 1 << 16); // 亮度对比度饱和度
    }

    // Set BGRA data
    uint8_t *bgra_data[1] = {const_cast<uint8_t *>(frame_data.data())};
    int bgra_linesize[1] = {width * 4};

    // Convert BGRA to YUV
    sws_scale(sws_context_, bgra_data, bgra_linesize, 0, height,
              frame_->data, frame_->linesize);

    // Set timestamp
    frame_->pts = frame_count_;
    frame_count_++;

    // Send frame to encoder
    int ret = avcodec_send_frame(codec_context_, frame_);
    if (ret < 0)
    {
        char error_msg[AV_ERROR_MAX_STRING_SIZE];
        av_strerror(ret, error_msg, AV_ERROR_MAX_STRING_SIZE);
        std::cerr << "Failed to send frame to encoder: " << error_msg << std::endl;
        return false;
    }

    // Receive encoded packets
    while (ret >= 0)
    {
        ret = avcodec_receive_packet(codec_context_, packet_);
        if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF)
        {
            break;
        }
        else if (ret < 0)
        {
            char error_msg[AV_ERROR_MAX_STRING_SIZE];
            av_strerror(ret, error_msg, AV_ERROR_MAX_STRING_SIZE);
            std::cerr << "Failed to receive packet from encoder: " << error_msg << std::endl;
            return false;
        }

        // Set packet timestamp and ensure PTS >= DTS
        packet_->stream_index = stream_->index;
        av_packet_rescale_ts(packet_, codec_context_->time_base, stream_->time_base);

        // 确保PTS >= DTS，避免时间戳错误
        if (packet_->pts < packet_->dts)
        {
            packet_->pts = packet_->dts;
        }

        // Write packet with retry mechanism
        int write_retry_count = 0;
        const int max_retries = 3;

        while (write_retry_count < max_retries)
        {
            ret = av_interleaved_write_frame(format_context_, packet_);
            if (ret >= 0)
            {
                // Success
                break;
            }
            else
            {
                char error_msg[AV_ERROR_MAX_STRING_SIZE];
                av_strerror(ret, error_msg, AV_ERROR_MAX_STRING_SIZE);

                // Check if it's a network error
                if (ret == -10053 || ret == -10054 || ret == -10060) // Network errors
                {
                    write_retry_count++;
                    std::cerr << "Network error (attempt " << write_retry_count << "/" << max_retries << "): " << error_msg << std::endl;

                    if (write_retry_count < max_retries)
                    {
                        std::cout << "Retrying in 1 second..." << std::endl;
                        std::this_thread::sleep_for(std::chrono::seconds(1));

                        // Try to reconnect
                        if (format_context_->pb)
                        {
                            avio_closep(&format_context_->pb);
                        }

                        // Reopen connection
                        AVDictionary *options = nullptr;
                        av_dict_set(&options, "timeout", "5000000", 0);
                        av_dict_set(&options, "reconnect", "1", 0);

                        ret = avio_open(&format_context_->pb, format_context_->url, AVIO_FLAG_WRITE);
                        if (ret >= 0)
                        {
                            std::cout << "Reconnected successfully" << std::endl;
                        }
                        else
                        {
                            std::cerr << "Failed to reconnect" << std::endl;
                        }

                        if (av_dict_get(options, nullptr, nullptr, AV_DICT_IGNORE_SUFFIX))
                        {
                            av_dict_free(&options);
                        }
                    }
                }
                else
                {
                    // Non-network error, don't retry
                    std::cerr << "Failed to write packet: " << error_msg << " (code: " << ret << ")" << std::endl;
                    std::cerr << "Error type: " << (ret == -10053 ? "Connection aborted" : ret == -10054 ? "Connection reset"
                                                                                       : ret == -10060   ? "Connection timed out"
                                                                                                         : "Other error")
                              << std::endl;
                    return false;
                }
            }
        }

        if (write_retry_count >= max_retries)
        {
            std::cerr << "Failed to write packet after " << max_retries << " retries" << std::endl;
            return false;
        }
    }

    return true;
}

void StreamPusher::Close()
{
    if (initialized_)
    {
        // Flush encoder
        avcodec_send_frame(codec_context_, nullptr);

        // Receive remaining packets
        while (true)
        {
            int ret = avcodec_receive_packet(codec_context_, packet_);
            if (ret == AVERROR_EOF)
            {
                break;
            }
            else if (ret < 0)
            {
                break;
            }

            packet_->stream_index = stream_->index;
            av_packet_rescale_ts(packet_, codec_context_->time_base, stream_->time_base);
            av_interleaved_write_frame(format_context_, packet_);
        }

        // Write stream trailer
        av_write_trailer(format_context_);
    }

    Cleanup();
}

void StreamPusher::Cleanup()
{
    if (sws_context_)
    {
        sws_freeContext(sws_context_);
        sws_context_ = nullptr;
    }

    if (packet_)
    {
        av_packet_free(&packet_);
        packet_ = nullptr;
    }

    if (frame_)
    {
        av_frame_free(&frame_);
        frame_ = nullptr;
    }

    if (codec_context_)
    {
        avcodec_free_context(&codec_context_);
        codec_context_ = nullptr;
    }

    if (format_context_)
    {
        if (format_context_->pb)
        {
            avio_closep(&format_context_->pb);
        }
        avformat_free_context(format_context_);
        format_context_ = nullptr;
    }

    initialized_ = false;
}
