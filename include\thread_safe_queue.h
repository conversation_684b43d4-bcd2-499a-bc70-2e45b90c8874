#pragma once

#include <queue>
#include <mutex>
#include <condition_variable>
#include <chrono>
#include <atomic>

/**
 * @brief 线程安全队列模板类
 * 
 * 支持多生产者多消费者的线程安全队列，具有超时控制和大小限制
 */
template<typename T>
class ThreadSafeQueue
{
public:
    /**
     * @brief 构造函数
     * @param max_size 队列最大大小，0表示无限制
     */
    explicit ThreadSafeQueue(size_t max_size = 0)
        : max_size_(max_size), closed_(false)
    {
    }

    /**
     * @brief 析构函数
     */
    ~ThreadSafeQueue()
    {
        Close();
    }

    /**
     * @brief 向队列中添加元素
     * @param item 要添加的元素
     * @param timeout 超时时间
     * @return 是否成功添加
     */
    bool Push(T&& item, std::chrono::milliseconds timeout = std::chrono::milliseconds(100))
    {
        std::unique_lock<std::mutex> lock(mutex_);
        
        if (closed_) {
            return false;
        }
        
        // 如果队列已满，等待或超时
        if (max_size_ > 0) {
            bool success = not_full_.wait_for(lock, timeout, [this] {
                return queue_.size() < max_size_ || closed_;
            });
            
            if (!success || closed_) {
                return false;
            }
        }
        
        queue_.push(std::move(item));
        not_empty_.notify_one();
        return true;
    }

    /**
     * @brief 从队列中取出元素
     * @param item 输出参数，存储取出的元素
     * @param timeout 超时时间
     * @return 是否成功取出
     */
    bool Pop(T& item, std::chrono::milliseconds timeout = std::chrono::milliseconds(100))
    {
        std::unique_lock<std::mutex> lock(mutex_);
        
        bool success = not_empty_.wait_for(lock, timeout, [this] {
            return !queue_.empty() || closed_;
        });
        
        if (!success || (queue_.empty() && closed_)) {
            return false;
        }
        
        if (!queue_.empty()) {
            item = std::move(queue_.front());
            queue_.pop();
            not_full_.notify_one();
            return true;
        }
        
        return false;
    }

    /**
     * @brief 尝试立即取出元素（非阻塞）
     * @param item 输出参数，存储取出的元素
     * @return 是否成功取出
     */
    bool TryPop(T& item)
    {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (queue_.empty()) {
            return false;
        }
        
        item = std::move(queue_.front());
        queue_.pop();
        not_full_.notify_one();
        return true;
    }

    /**
     * @brief 尝试立即添加元素（非阻塞）
     * @param item 要添加的元素
     * @return 是否成功添加
     */
    bool TryPush(T&& item)
    {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (closed_ || (max_size_ > 0 && queue_.size() >= max_size_)) {
            return false;
        }
        
        queue_.push(std::move(item));
        not_empty_.notify_one();
        return true;
    }

    /**
     * @brief 获取队列当前大小
     * @return 队列大小
     */
    size_t Size() const
    {
        std::lock_guard<std::mutex> lock(mutex_);
        return queue_.size();
    }

    /**
     * @brief 检查队列是否为空
     * @return 是否为空
     */
    bool Empty() const
    {
        std::lock_guard<std::mutex> lock(mutex_);
        return queue_.empty();
    }

    /**
     * @brief 检查队列是否已满
     * @return 是否已满
     */
    bool Full() const
    {
        std::lock_guard<std::mutex> lock(mutex_);
        return max_size_ > 0 && queue_.size() >= max_size_;
    }

    /**
     * @brief 清空队列
     */
    void Clear()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        std::queue<T> empty_queue;
        queue_.swap(empty_queue);
        not_full_.notify_all();
    }

    /**
     * @brief 关闭队列
     * 
     * 关闭后不能再添加元素，但可以继续取出已有元素
     */
    void Close()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        closed_ = true;
        not_empty_.notify_all();
        not_full_.notify_all();
    }

    /**
     * @brief 检查队列是否已关闭
     * @return 是否已关闭
     */
    bool IsClosed() const
    {
        std::lock_guard<std::mutex> lock(mutex_);
        return closed_;
    }

    /**
     * @brief 重新打开队列
     */
    void Reopen()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        closed_ = false;
    }

    /**
     * @brief 设置队列最大大小
     * @param max_size 最大大小，0表示无限制
     */
    void SetMaxSize(size_t max_size)
    {
        std::lock_guard<std::mutex> lock(mutex_);
        max_size_ = max_size;
        not_full_.notify_all();
    }

    /**
     * @brief 获取队列最大大小
     * @return 最大大小
     */
    size_t GetMaxSize() const
    {
        std::lock_guard<std::mutex> lock(mutex_);
        return max_size_;
    }

private:
    mutable std::mutex mutex_;
    std::condition_variable not_empty_;
    std::condition_variable not_full_;
    std::queue<T> queue_;
    size_t max_size_;
    std::atomic<bool> closed_;

    // 禁用拷贝构造和赋值
    ThreadSafeQueue(const ThreadSafeQueue&) = delete;
    ThreadSafeQueue& operator=(const ThreadSafeQueue&) = delete;
};

/**
 * @brief 高性能无锁队列（单生产者单消费者）
 * 
 * 适用于对性能要求极高的场景，但仅支持单生产者单消费者
 */
template<typename T>
class LockFreeQueue
{
private:
    struct Node
    {
        std::atomic<T*> data;
        std::atomic<Node*> next;
        
        Node() : data(nullptr), next(nullptr) {}
    };

public:
    LockFreeQueue()
    {
        Node* dummy = new Node;
        head_.store(dummy);
        tail_.store(dummy);
    }

    ~LockFreeQueue()
    {
        while (Node* old_head = head_.load()) {
            head_.store(old_head->next);
            delete old_head;
        }
    }

    /**
     * @brief 添加元素（仅支持单生产者）
     * @param item 要添加的元素
     */
    void Push(T&& item)
    {
        Node* new_node = new Node;
        T* data = new T(std::move(item));
        
        Node* prev_tail = tail_.exchange(new_node);
        prev_tail->data.store(data);
        prev_tail->next.store(new_node);
    }

    /**
     * @brief 取出元素（仅支持单消费者）
     * @param item 输出参数
     * @return 是否成功取出
     */
    bool Pop(T& item)
    {
        Node* head = head_.load();
        Node* next = head->next.load();
        
        if (next == nullptr) {
            return false;
        }
        
        T* data = next->data.exchange(nullptr);
        if (data == nullptr) {
            return false;
        }
        
        item = std::move(*data);
        delete data;
        
        head_.store(next);
        delete head;
        
        return true;
    }

    /**
     * @brief 检查队列是否为空
     * @return 是否为空
     */
    bool Empty() const
    {
        Node* head = head_.load();
        Node* next = head->next.load();
        return next == nullptr;
    }

private:
    std::atomic<Node*> head_;
    std::atomic<Node*> tail_;

    // 禁用拷贝构造和赋值
    LockFreeQueue(const LockFreeQueue&) = delete;
    LockFreeQueue& operator=(const LockFreeQueue&) = delete;
};
