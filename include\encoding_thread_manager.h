#pragma once

#include "thread_safe_queue.h"
#include "frame_data.h"
#include "hardware_encoder_manager.h"
#include <thread>
#include <atomic>
#include <chrono>
#include <memory>

extern "C" {
#include <libavcodec/avcodec.h>
#include <libavutil/frame.h>
}

/**
 * @brief 编码线程管理器
 * 
 * 负责在独立线程中进行视频编码，从帧队列取出原始帧，编码后放入包队列
 */
class EncodingThreadManager
{
public:
    /**
     * @brief 编码统计信息
     */
    struct EncodingStats
    {
        std::atomic<uint64_t> total_frames{0};
        std::atomic<uint64_t> encoded_frames{0};
        std::atomic<uint64_t> failed_frames{0};
        std::atomic<uint64_t> keyframes{0};
        std::atomic<double> avg_encoding_time_ms{0.0};
        std::atomic<double> max_encoding_time_ms{0.0};
        std::atomic<double> current_fps{0.0};
        std::atomic<size_t> total_encoded_bytes{0};
        std::chrono::steady_clock::time_point start_time;
        
        void Reset()
        {
            total_frames = 0;
            encoded_frames = 0;
            failed_frames = 0;
            keyframes = 0;
            avg_encoding_time_ms = 0.0;
            max_encoding_time_ms = 0.0;
            current_fps = 0.0;
            total_encoded_bytes = 0;
            start_time = std::chrono::steady_clock::now();
        }
        
        double GetSuccessRate() const
        {
            uint64_t total = total_frames.load();
            return total > 0 ? (double)encoded_frames.load() / total * 100.0 : 0.0;
        }
        
        double GetActualFPS() const
        {
            auto now = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - start_time);
            return duration.count() > 0 ? (double)encoded_frames.load() / duration.count() : 0.0;
        }
        
        double GetAverageBitrate() const
        {
            auto now = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - start_time);
            return duration.count() > 0 ? (double)total_encoded_bytes.load() * 8 / duration.count() : 0.0;
        }
    };

public:
    /**
     * @brief 构造函数
     * @param input_queue 输入帧队列引用
     * @param output_queue 输出包队列引用
     * @param output_queue_max_size 输出队列最大大小
     */
    EncodingThreadManager(ThreadSafeQueue<std::unique_ptr<CaptureFrame>>& input_queue,
                         ThreadSafeQueue<std::unique_ptr<EncodedPacket>>& output_queue,
                         size_t output_queue_max_size = 20)
        : input_queue_(input_queue)
        , output_queue_(output_queue)
        , running_(false)
        , codec_context_(nullptr)
        , encode_frame_(nullptr)
        , packet_(nullptr)
        , frame_width_(0)
        , frame_height_(0)
        , frame_rate_(30)
        , pts_counter_(0)
    {
        output_queue_.SetMaxSize(output_queue_max_size);
        encoder_manager_ = std::make_unique<HardwareEncoderManager>();
        pixel_converter_ = std::make_unique<HardwarePixelConverter>();
    }

    /**
     * @brief 析构函数
     */
    ~EncodingThreadManager()
    {
        Stop();
    }

    /**
     * @brief 启动编码线程
     * @param encoder_type 编码器类型 ("auto", "nvenc", "amf", "qsv", "software")
     * @param width 视频宽度
     * @param height 视频高度
     * @param fps 帧率
     * @param bitrate 码率 (0表示自动计算)
     * @return 是否启动成功
     */
    bool Start(const std::string& encoder_type, int width, int height, int fps, int bitrate = 0)
    {
        if (running_) {
            return false;
        }

        frame_width_ = width;
        frame_height_ = height;
        frame_rate_ = fps;

        // 初始化编码器
        if (!InitializeEncoder(encoder_type, width, height, fps, bitrate)) {
            return false;
        }

        // 重置统计信息
        stats_.Reset();
        pts_counter_ = 0;

        // 启动编码线程
        running_ = true;
        encoding_thread_ = std::thread(&EncodingThreadManager::EncodingLoop, this);

        return true;
    }

    /**
     * @brief 停止编码线程
     */
    void Stop()
    {
        if (!running_) {
            return;
        }

        running_ = false;
        
        if (encoding_thread_.joinable()) {
            encoding_thread_.join();
        }

        CleanupEncoder();
    }

    /**
     * @brief 检查是否正在运行
     * @return 是否正在运行
     */
    bool IsRunning() const
    {
        return running_;
    }

    /**
     * @brief 获取编码统计信息
     * @return 统计信息
     */
    const EncodingStats& GetStats() const
    {
        return stats_;
    }

    /**
     * @brief 获取当前编码器信息
     * @return 编码器信息
     */
    std::string GetEncoderInfo() const
    {
        if (encoder_manager_) {
            auto metrics = encoder_manager_->GetMetrics();
            return metrics.encoder_name;
        }
        return "Unknown";
    }

    /**
     * @brief 获取输入队列大小
     * @return 队列大小
     */
    size_t GetInputQueueSize() const
    {
        return input_queue_.Size();
    }

    /**
     * @brief 获取输出队列大小
     * @return 队列大小
     */
    size_t GetOutputQueueSize() const
    {
        return output_queue_.Size();
    }

    /**
     * @brief 刷新编码器（发送所有缓存的帧）
     */
    void FlushEncoder()
    {
        if (!codec_context_) return;

        // 发送NULL帧来刷新编码器
        int ret = avcodec_send_frame(codec_context_, nullptr);
        if (ret < 0) return;

        // 接收所有剩余的包
        while (true) {
            ret = avcodec_receive_packet(codec_context_, packet_);
            if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF) {
                break;
            }
            if (ret < 0) {
                break;
            }

            // 创建编码包并放入队列
            auto encoded_packet = CreateEncodedPacket();
            if (encoded_packet) {
                output_queue_.TryPush(std::move(encoded_packet));
            }

            av_packet_unref(packet_);
        }
    }

private:
    /**
     * @brief 初始化编码器
     */
    bool InitializeEncoder(const std::string& encoder_type, int width, int height, int fps, int bitrate)
    {
        // 检测可用编码器
        auto available_encoders = encoder_manager_->DetectAvailableEncoders();
        if (available_encoders.empty()) {
            return false;
        }

        // 选择编码器类型
        HardwareEncoderManager::EncoderType selected_type;
        if (encoder_type == "auto") {
            selected_type = encoder_manager_->SelectBestEncoder(width, height, fps, false);
        } else if (encoder_type == "nvenc") {
            selected_type = HardwareEncoderManager::ENCODER_NVENC;
        } else if (encoder_type == "amf") {
            selected_type = HardwareEncoderManager::ENCODER_AMF;
        } else if (encoder_type == "qsv") {
            selected_type = HardwareEncoderManager::ENCODER_QSV;
        } else if (encoder_type == "software") {
            selected_type = HardwareEncoderManager::ENCODER_SOFTWARE;
        } else {
            selected_type = encoder_manager_->SelectBestEncoder(width, height, fps, false);
        }

        // 创建编码器上下文
        codec_context_ = encoder_manager_->CreateEncoderContext(selected_type, width, height, fps, bitrate);
        if (!codec_context_) {
            return false;
        }

        // 打开编码器
        int ret = avcodec_open2(codec_context_, codec_context_->codec, nullptr);
        if (ret < 0) {
            avcodec_free_context(&codec_context_);
            return false;
        }

        // 创建帧和包
        encode_frame_ = av_frame_alloc();
        packet_ = av_packet_alloc();
        if (!encode_frame_ || !packet_) {
            CleanupEncoder();
            return false;
        }

        // 设置帧参数
        encode_frame_->format = codec_context_->pix_fmt;
        encode_frame_->width = width;
        encode_frame_->height = height;
        ret = av_frame_get_buffer(encode_frame_, 0);
        if (ret < 0) {
            CleanupEncoder();
            return false;
        }

        // 初始化像素格式转换器
        if (!pixel_converter_->Initialize(width, height, codec_context_->pix_fmt)) {
            CleanupEncoder();
            return false;
        }

        // 设置当前编码器
        encoder_manager_->SetCurrentEncoder(selected_type);
        encoder_manager_->ResetMetrics();

        return true;
    }

    /**
     * @brief 清理编码器资源
     */
    void CleanupEncoder()
    {
        if (encode_frame_) {
            av_frame_free(&encode_frame_);
        }
        if (packet_) {
            av_packet_free(&packet_);
        }
        if (codec_context_) {
            avcodec_free_context(&codec_context_);
        }
        if (pixel_converter_) {
            pixel_converter_->Cleanup();
        }
    }

    /**
     * @brief 编码循环（在独立线程中运行）
     */
    void EncodingLoop()
    {
        while (running_) {
            std::unique_ptr<CaptureFrame> frame;
            
            // 从输入队列获取帧
            if (!input_queue_.Pop(frame, std::chrono::milliseconds(100))) {
                continue;
            }

            if (!frame || !frame->IsValid()) {
                stats_.failed_frames++;
                continue;
            }

            auto encoding_start = std::chrono::steady_clock::now();
            
            // 编码帧
            bool success = EncodeFrame(*frame);
            
            auto encoding_end = std::chrono::steady_clock::now();
            auto encoding_duration = std::chrono::duration_cast<std::chrono::microseconds>(
                encoding_end - encoding_start);

            // 更新统计信息
            UpdateStats(encoding_duration.count() / 1000.0, success);
            
            // 更新编码器指标
            if (encoder_manager_) {
                encoder_manager_->UpdateMetrics(encoding_duration.count() / 1000.0, success);
            }
        }

        // 刷新编码器
        FlushEncoder();
    }

    /**
     * @brief 编码单帧
     */
    bool EncodeFrame(const CaptureFrame& frame)
    {
        try {
            stats_.total_frames++;

            // 转换像素格式
            if (!pixel_converter_->ConvertBGRAToFrame(frame.data, encode_frame_)) {
                stats_.failed_frames++;
                return false;
            }

            // 设置时间戳
            encode_frame_->pts = pts_counter_++;

            // 发送帧到编码器
            int ret = avcodec_send_frame(codec_context_, encode_frame_);
            if (ret < 0) {
                stats_.failed_frames++;
                return false;
            }

            // 接收编码后的包
            bool packet_received = false;
            while (true) {
                ret = avcodec_receive_packet(codec_context_, packet_);
                if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF) {
                    break;
                }
                if (ret < 0) {
                    stats_.failed_frames++;
                    return false;
                }

                // 创建编码包
                auto encoded_packet = CreateEncodedPacket();
                if (encoded_packet) {
                    encoded_packet->frame_number = frame.frame_number;
                    
                    // 尝试放入输出队列
                    if (output_queue_.TryPush(std::move(encoded_packet))) {
                        packet_received = true;
                        stats_.total_encoded_bytes += packet_->size;
                        
                        if (packet_->flags & AV_PKT_FLAG_KEY) {
                            stats_.keyframes++;
                        }
                    }
                }

                av_packet_unref(packet_);
            }

            if (packet_received) {
                stats_.encoded_frames++;
                return true;
            }

            return false;

        } catch (...) {
            stats_.failed_frames++;
            return false;
        }
    }

    /**
     * @brief 创建编码包
     */
    std::unique_ptr<EncodedPacket> CreateEncodedPacket()
    {
        auto encoded_packet = std::make_unique<EncodedPacket>();
        
        encoded_packet->data.resize(packet_->size);
        std::memcpy(encoded_packet->data.data(), packet_->data, packet_->size);
        encoded_packet->data_size = packet_->size;
        encoded_packet->pts = packet_->pts;
        encoded_packet->dts = packet_->dts;
        encoded_packet->is_keyframe = (packet_->flags & AV_PKT_FLAG_KEY) != 0;
        encoded_packet->timestamp = std::chrono::steady_clock::now();
        
        return encoded_packet;
    }

    /**
     * @brief 更新统计信息
     */
    void UpdateStats(double encoding_time_ms, bool success)
    {
        if (success) {
            // 更新平均编码时间
            double current_avg = stats_.avg_encoding_time_ms.load();
            double new_avg = (current_avg * (stats_.encoded_frames.load() - 1) + encoding_time_ms) / 
                           stats_.encoded_frames.load();
            stats_.avg_encoding_time_ms = new_avg;
            
            // 更新最大编码时间
            double current_max = stats_.max_encoding_time_ms.load();
            if (encoding_time_ms > current_max) {
                stats_.max_encoding_time_ms = encoding_time_ms;
            }
        }
        
        // 每100帧更新一次FPS
        if (stats_.total_frames % 100 == 0) {
            stats_.current_fps = stats_.GetActualFPS();
        }
    }

private:
    // 核心组件
    std::unique_ptr<HardwareEncoderManager> encoder_manager_;
    std::unique_ptr<HardwarePixelConverter> pixel_converter_;
    ThreadSafeQueue<std::unique_ptr<CaptureFrame>>& input_queue_;
    ThreadSafeQueue<std::unique_ptr<EncodedPacket>>& output_queue_;

    // 线程控制
    std::thread encoding_thread_;
    std::atomic<bool> running_;

    // 编码器组件
    AVCodecContext* codec_context_;
    AVFrame* encode_frame_;
    AVPacket* packet_;

    // 编码参数
    int frame_width_, frame_height_, frame_rate_;
    std::atomic<int64_t> pts_counter_;

    // 统计信息
    EncodingStats stats_;

    // 禁用拷贝构造和赋值
    EncodingThreadManager(const EncodingThreadManager&) = delete;
    EncodingThreadManager& operator=(const EncodingThreadManager&) = delete;
};
