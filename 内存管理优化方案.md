# 内存管理和缓冲池优化方案

## 概述

内存管理是高性能视频推流系统的关键组件。频繁的内存分配和释放会导致性能下降、内存碎片和不稳定的延迟。本方案通过实现高效的内存池管理系统，大幅减少内存分配开销，提升系统性能和稳定性。

## 当前内存使用问题

### 1. 性能问题
- **频繁分配**: 每帧都需要分配大量内存（1080p帧约8MB）
- **内存碎片**: 长时间运行导致内存碎片化
- **分配延迟**: malloc/free调用增加处理延迟
- **缓存失效**: 内存不连续导致CPU缓存效率低

### 2. 内存使用模式分析
```
帧数据: 1920x1080x4 = 8.3MB per frame
编码缓冲: 可变大小，通常100KB-2MB
队列缓冲: 10-20帧 = 83-166MB
总内存需求: 200-300MB
```

## 优化架构设计

### 1. 分层内存管理

```
应用层
    ↓
智能指针层 (RAII)
    ↓
内存池管理层
    ↓
底层内存分配器
    ↓
系统内存
```

### 2. 核心组件

#### 2.1 通用内存池
```cpp
template<typename T>
class MemoryPool {
private:
    struct Block {
        alignas(T) char data[sizeof(T)];
        Block* next;
    };
    
    Block* free_list_;
    std::vector<std::unique_ptr<Block[]>> chunks_;
    size_t chunk_size_;
    std::mutex pool_mutex_;
    
public:
    T* Allocate();
    void Deallocate(T* ptr);
    void PreAllocate(size_t count);
};
```

#### 2.2 大块内存分配器
```cpp
class LargeBlockAllocator {
private:
    struct MemoryBlock {
        void* data;
        size_t size;
        bool in_use;
        std::chrono::steady_clock::time_point last_used;
    };
    
    std::vector<MemoryBlock> blocks_;
    std::mutex allocator_mutex_;
    size_t total_allocated_;
    size_t max_memory_limit_;
    
public:
    void* Allocate(size_t size, size_t alignment = 32);
    void Deallocate(void* ptr);
    void Cleanup(); // 清理长时间未使用的块
};
```

#### 2.3 帧缓冲池管理器
```cpp
class FrameBufferPoolManager {
private:
    struct FrameBufferInfo {
        int width;
        int height;
        size_t data_size;
        std::unique_ptr<MemoryPool<uint8_t[]>> pool;
    };
    
    std::map<std::pair<int,int>, FrameBufferInfo> buffer_pools_;
    std::unique_ptr<LargeBlockAllocator> large_allocator_;
    std::mutex manager_mutex_;
    
public:
    std::unique_ptr<CaptureFrame> GetFrame(int width, int height);
    void ReturnFrame(std::unique_ptr<CaptureFrame> frame);
    void OptimizePools(); // 动态调整池大小
};
```

## 详细实现

### 1. 高性能内存池

```cpp
template<typename T>
class HighPerformanceMemoryPool {
public:
    class PooledObject {
    private:
        HighPerformanceMemoryPool* pool_;
        T* object_;
        
    public:
        PooledObject(HighPerformanceMemoryPool* pool, T* obj) 
            : pool_(pool), object_(obj) {}
        
        ~PooledObject() {
            if (pool_ && object_) {
                pool_->Return(object_);
            }
        }
        
        T* Get() { return object_; }
        T* operator->() { return object_; }
        T& operator*() { return *object_; }
        
        // 移动语义
        PooledObject(PooledObject&& other) noexcept
            : pool_(other.pool_), object_(other.object_) {
            other.pool_ = nullptr;
            other.object_ = nullptr;
        }
        
        PooledObject& operator=(PooledObject&& other) noexcept {
            if (this != &other) {
                if (pool_ && object_) {
                    pool_->Return(object_);
                }
                pool_ = other.pool_;
                object_ = other.object_;
                other.pool_ = nullptr;
                other.object_ = nullptr;
            }
            return *this;
        }
        
        // 禁用拷贝
        PooledObject(const PooledObject&) = delete;
        PooledObject& operator=(const PooledObject&) = delete;
    };

private:
    struct alignas(64) PoolBlock {  // 缓存行对齐
        T object;
        std::atomic<bool> in_use{false};
        std::atomic<PoolBlock*> next{nullptr};
    };
    
    std::unique_ptr<PoolBlock[]> blocks_;
    std::atomic<PoolBlock*> free_head_{nullptr};
    size_t pool_size_;
    std::atomic<size_t> allocated_count_{0};
    std::atomic<size_t> peak_usage_{0};
    
public:
    HighPerformanceMemoryPool(size_t initial_size = 32) 
        : pool_size_(initial_size) {
        Initialize();
    }
    
    ~HighPerformanceMemoryPool() {
        // 确保所有对象都已归还
        while (allocated_count_.load() > 0) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
    }
    
    PooledObject Acquire() {
        T* obj = AcquireRaw();
        return PooledObject(this, obj);
    }
    
    T* AcquireRaw() {
        PoolBlock* block = nullptr;
        
        // 尝试从空闲链表获取
        do {
            block = free_head_.load();
            if (!block) {
                // 池已空，扩展池大小
                if (!ExpandPool()) {
                    return nullptr;
                }
                block = free_head_.load();
            }
        } while (block && !free_head_.compare_exchange_weak(block, block->next.load()));
        
        if (block) {
            block->in_use.store(true);
            allocated_count_.fetch_add(1);
            
            // 更新峰值使用量
            size_t current = allocated_count_.load();
            size_t peak = peak_usage_.load();
            while (current > peak && !peak_usage_.compare_exchange_weak(peak, current)) {
                peak = peak_usage_.load();
            }
            
            return &block->object;
        }
        
        return nullptr;
    }
    
    void Return(T* obj) {
        if (!obj) return;
        
        // 计算块地址
        PoolBlock* block = reinterpret_cast<PoolBlock*>(
            reinterpret_cast<char*>(obj) - offsetof(PoolBlock, object));
        
        if (!block->in_use.exchange(false)) {
            // 双重释放检测
            return;
        }
        
        // 重置对象状态（如果需要）
        if constexpr (std::is_destructible_v<T>) {
            obj->~T();
            new (obj) T(); // 重新构造
        }
        
        // 添加到空闲链表
        PoolBlock* head;
        do {
            head = free_head_.load();
            block->next.store(head);
        } while (!free_head_.compare_exchange_weak(head, block));
        
        allocated_count_.fetch_sub(1);
    }
    
    // 统计信息
    size_t GetAllocatedCount() const { return allocated_count_.load(); }
    size_t GetPeakUsage() const { return peak_usage_.load(); }
    size_t GetPoolSize() const { return pool_size_; }
    double GetUtilization() const { 
        return (double)allocated_count_.load() / pool_size_; 
    }

private:
    void Initialize() {
        blocks_ = std::make_unique<PoolBlock[]>(pool_size_);
        
        // 构建空闲链表
        for (size_t i = 0; i < pool_size_ - 1; ++i) {
            blocks_[i].next.store(&blocks_[i + 1]);
        }
        blocks_[pool_size_ - 1].next.store(nullptr);
        
        free_head_.store(&blocks_[0]);
    }
    
    bool ExpandPool() {
        // 简化实现：不支持动态扩展
        // 实际实现中可以分配新的块数组
        return false;
    }
};
```

### 2. 智能帧缓冲管理

```cpp
class SmartFrameBuffer {
public:
    using FramePtr = std::unique_ptr<CaptureFrame, std::function<void(CaptureFrame*)>>;
    
private:
    struct BufferPool {
        std::unique_ptr<HighPerformanceMemoryPool<CaptureFrame>> frame_pool;
        std::unique_ptr<LargeBlockAllocator> data_allocator;
        int width, height;
        size_t frame_size;
        std::atomic<size_t> hit_count{0};
        std::atomic<size_t> miss_count{0};
        std::chrono::steady_clock::time_point last_access;
    };
    
    std::unordered_map<uint64_t, std::unique_ptr<BufferPool>> pools_;
    std::mutex pools_mutex_;
    std::thread cleanup_thread_;
    std::atomic<bool> cleanup_running_{false};
    
    // 配置参数
    size_t max_pools_;
    size_t pool_initial_size_;
    std::chrono::minutes pool_timeout_;

public:
    SmartFrameBuffer(size_t max_pools = 10, size_t pool_initial_size = 16)
        : max_pools_(max_pools), pool_initial_size_(pool_initial_size)
        , pool_timeout_(std::chrono::minutes(5)) {
        StartCleanupThread();
    }
    
    ~SmartFrameBuffer() {
        StopCleanupThread();
    }
    
    FramePtr AcquireFrame(int width, int height) {
        uint64_t key = MakeKey(width, height);
        BufferPool* pool = GetOrCreatePool(key, width, height);
        
        if (!pool) {
            return nullptr;
        }
        
        auto pooled_frame = pool->frame_pool->Acquire();
        CaptureFrame* frame = pooled_frame.Get();
        
        if (!frame) {
            pool->miss_count.fetch_add(1);
            return nullptr;
        }
        
        // 分配数据缓冲区
        size_t data_size = width * height * 4;
        void* data_ptr = pool->data_allocator->Allocate(data_size, 32);
        
        if (!data_ptr) {
            pool->miss_count.fetch_add(1);
            return nullptr;
        }
        
        // 初始化帧
        frame->width = width;
        frame->height = height;
        frame->data_size = data_size;
        frame->data.clear();
        frame->data.reserve(data_size);
        frame->timestamp = std::chrono::steady_clock::now();
        
        pool->hit_count.fetch_add(1);
        pool->last_access = std::chrono::steady_clock::now();
        
        // 创建智能指针，自动归还到池
        return FramePtr(frame, [this, pool](CaptureFrame* f) {
            if (f && pool) {
                pool->data_allocator->Deallocate(f->data.data());
                f->Reset();
                pool->frame_pool->Return(f);
            }
        });
    }
    
    // 获取统计信息
    struct PoolStats {
        size_t total_pools;
        size_t total_frames_allocated;
        size_t total_hit_count;
        size_t total_miss_count;
        double average_hit_rate;
        size_t total_memory_used;
    };
    
    PoolStats GetStats() const {
        std::lock_guard<std::mutex> lock(pools_mutex_);
        
        PoolStats stats{};
        stats.total_pools = pools_.size();
        
        for (const auto& [key, pool] : pools_) {
            stats.total_frames_allocated += pool->frame_pool->GetAllocatedCount();
            stats.total_hit_count += pool->hit_count.load();
            stats.total_miss_count += pool->miss_count.load();
            stats.total_memory_used += pool->frame_size * pool->frame_pool->GetPoolSize();
        }
        
        size_t total_requests = stats.total_hit_count + stats.total_miss_count;
        stats.average_hit_rate = total_requests > 0 ? 
            (double)stats.total_hit_count / total_requests : 0.0;
        
        return stats;
    }

private:
    uint64_t MakeKey(int width, int height) const {
        return (static_cast<uint64_t>(width) << 32) | static_cast<uint64_t>(height);
    }
    
    BufferPool* GetOrCreatePool(uint64_t key, int width, int height) {
        std::lock_guard<std::mutex> lock(pools_mutex_);
        
        auto it = pools_.find(key);
        if (it != pools_.end()) {
            return it->second.get();
        }
        
        // 检查池数量限制
        if (pools_.size() >= max_pools_) {
            CleanupOldPools();
            if (pools_.size() >= max_pools_) {
                return nullptr; // 无法创建新池
            }
        }
        
        // 创建新池
        auto pool = std::make_unique<BufferPool>();
        pool->frame_pool = std::make_unique<HighPerformanceMemoryPool<CaptureFrame>>(pool_initial_size_);
        pool->data_allocator = std::make_unique<LargeBlockAllocator>();
        pool->width = width;
        pool->height = height;
        pool->frame_size = width * height * 4;
        pool->last_access = std::chrono::steady_clock::now();
        
        BufferPool* pool_ptr = pool.get();
        pools_[key] = std::move(pool);
        
        return pool_ptr;
    }
    
    void CleanupOldPools() {
        auto now = std::chrono::steady_clock::now();
        
        auto it = pools_.begin();
        while (it != pools_.end()) {
            auto age = std::chrono::duration_cast<std::chrono::minutes>(
                now - it->second->last_access);
            
            if (age > pool_timeout_ && it->second->frame_pool->GetAllocatedCount() == 0) {
                it = pools_.erase(it);
            } else {
                ++it;
            }
        }
    }
    
    void StartCleanupThread() {
        cleanup_running_ = true;
        cleanup_thread_ = std::thread([this] {
            while (cleanup_running_) {
                std::this_thread::sleep_for(std::chrono::minutes(1));
                
                std::lock_guard<std::mutex> lock(pools_mutex_);
                CleanupOldPools();
            }
        });
    }
    
    void StopCleanupThread() {
        cleanup_running_ = false;
        if (cleanup_thread_.joinable()) {
            cleanup_thread_.join();
        }
    }
};
```

### 3. 内存使用监控

```cpp
class MemoryMonitor {
public:
    struct MemoryStats {
        size_t total_allocated;      // 总分配内存
        size_t peak_usage;          // 峰值使用量
        size_t current_usage;       // 当前使用量
        size_t pool_efficiency;     // 池效率百分比
        size_t fragmentation_ratio; // 碎片化比率
        double allocation_rate;     // 分配速率 (MB/s)
        double deallocation_rate;   // 释放速率 (MB/s)
    };

private:
    std::atomic<size_t> total_allocated_{0};
    std::atomic<size_t> peak_usage_{0};
    std::atomic<size_t> current_usage_{0};
    std::atomic<size_t> allocation_count_{0};
    std::atomic<size_t> deallocation_count_{0};
    
    std::chrono::steady_clock::time_point start_time_;
    mutable std::mutex stats_mutex_;

public:
    MemoryMonitor() : start_time_(std::chrono::steady_clock::now()) {}
    
    void RecordAllocation(size_t size) {
        total_allocated_.fetch_add(size);
        current_usage_.fetch_add(size);
        allocation_count_.fetch_add(1);
        
        // 更新峰值
        size_t current = current_usage_.load();
        size_t peak = peak_usage_.load();
        while (current > peak && !peak_usage_.compare_exchange_weak(peak, current)) {
            peak = peak_usage_.load();
        }
    }
    
    void RecordDeallocation(size_t size) {
        current_usage_.fetch_sub(size);
        deallocation_count_.fetch_add(1);
    }
    
    MemoryStats GetStats() const {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        
        auto now = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - start_time_);
        double seconds = duration.count();
        
        MemoryStats stats;
        stats.total_allocated = total_allocated_.load();
        stats.peak_usage = peak_usage_.load();
        stats.current_usage = current_usage_.load();
        stats.allocation_rate = seconds > 0 ? stats.total_allocated / seconds / 1024.0 / 1024.0 : 0.0;
        stats.deallocation_rate = seconds > 0 ? (stats.total_allocated - stats.current_usage) / seconds / 1024.0 / 1024.0 : 0.0;
        
        return stats;
    }
};
```

## 预期性能提升

### 内存性能对比
| 指标 | 传统分配 | 内存池 | 提升 |
|------|----------|--------|------|
| 分配延迟 | 10-100μs | 1-5μs | 90%+ |
| 内存碎片 | 高 | 极低 | 显著 |
| 缓存命中率 | 低 | 高 | 30-50% |
| 内存使用效率 | 70-80% | 90-95% | 20%+ |

### 系统性能提升
- **延迟降低**: 减少5-15ms处理延迟
- **吞吐量提升**: 提升20-40%帧处理能力
- **稳定性增强**: 减少内存相关崩溃
- **资源利用**: 更高效的内存使用

这个内存管理优化方案将显著提升系统性能，特别是在高负载和长时间运行场景下。
