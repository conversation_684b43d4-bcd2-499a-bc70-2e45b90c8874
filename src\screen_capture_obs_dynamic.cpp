#include "screen_capture_obs_dynamic.h"
#include "winrt_capture_manager.h"
#include <iostream>
#include <algorithm>
#include <d3d11.h>
#include <dxgi.h>
#include <windows.h>
#include <wingdi.h>

// Forward declaration of winrt_capture structure
struct winrt_capture
{
    // This structure is defined in OBS libobs-winrt
    // We'll use it as an opaque pointer
};

ScreenCaptureOBSDynamic::ScreenCaptureOBSDynamic()
    : winrt_capture_manager_(nullptr), current_capture_mode_(CAPTURE_MODE_NATIVE_WINRT), obs_winrt_dll_(nullptr), d3d11_dll_(nullptr), capture_(nullptr), selected_monitor_(nullptr), d3d11_device_(nullptr), d3d11_context_(nullptr), capture_x_(0), capture_y_(0), capture_width_(0), capture_height_(0), initialized_(false)
{
    // Initialize function pointers to nullptr
    winrt_capture_supported_ = nullptr;
    winrt_capture_init_monitor_ = nullptr;
    winrt_capture_free_ = nullptr;
    winrt_capture_active_ = nullptr;
    winrt_capture_render_ = nullptr;
    winrt_capture_width_ = nullptr;
    winrt_capture_height_ = nullptr;
    winrt_capture_thread_start_ = nullptr;
    winrt_capture_thread_stop_ = nullptr;

    d3d11_create_device_ = nullptr;
    d3d11_texture2d_get_desc_ = nullptr;
    d3d11_context_map_ = nullptr;
    d3d11_context_unmap_ = nullptr;
}

ScreenCaptureOBSDynamic::~ScreenCaptureOBSDynamic()
{
    Cleanup();
}

bool ScreenCaptureOBSDynamic::Initialize(int x, int y, int width, int height)
{
    Cleanup();

    capture_x_ = x;
    capture_y_ = y;
    capture_width_ = width;
    capture_height_ = height;

    std::cout << "Initializing enhanced screen capture with multiple fallback modes..." << std::endl;

    // 尝试1: 使用原生WinRT捕获 (最高性能)
    if (InitializeNativeWinRT(x, y, width, height))
    {
        current_capture_mode_ = CAPTURE_MODE_NATIVE_WINRT;
        initialized_ = true;
        std::cout << "✅ Native WinRT capture initialized successfully! (High Performance Mode)" << std::endl;
        return true;
    }

    std::cout << "⚠️ Native WinRT capture failed, trying OBS WinRT fallback..." << std::endl;

    // 尝试2: 使用OBS WinRT捕获 (中等性能)
    if (InitializeOBSWinRT(x, y, width, height))
    {
        current_capture_mode_ = CAPTURE_MODE_OBS_WINRT;
        initialized_ = true;
        std::cout << "✅ OBS WinRT capture initialized successfully! (Fallback Mode)" << std::endl;
        return true;
    }

    std::cout << "⚠️ OBS WinRT capture failed, trying GDI fallback..." << std::endl;

    // 尝试3: 使用GDI捕获 (最低性能但兼容性最好)
    if (InitializeGDICapture(x, y, width, height))
    {
        current_capture_mode_ = CAPTURE_MODE_GDI;
        initialized_ = true;
        std::cout << "✅ GDI capture initialized successfully! (Compatibility Mode)" << std::endl;
        return true;
    }

    std::cerr << "❌ All capture methods failed!" << std::endl;
    return false;
}

bool ScreenCaptureOBSDynamic::InitializeNativeWinRT(int x, int y, int width, int height)
{
    try
    {
        std::cout << "Attempting native WinRT capture initialization..." << std::endl;

        // 检查WinRT捕获是否支持
        if (!WinRTCaptureManager::IsWinRTCaptureSupported())
        {
            std::cout << "Native WinRT capture not supported on this system" << std::endl;
            return false;
        }

        // 创建WinRT捕获管理器
        winrt_capture_manager_ = std::make_unique<WinRTCaptureManager>();

        // 获取目标显示器
        HMONITOR target_monitor = GetMonitorAtPosition(x, y);
        if (!target_monitor)
        {
            target_monitor = GetPrimaryMonitor();
        }

        // 初始化WinRT捕获
        if (!winrt_capture_manager_->Initialize(nullptr, target_monitor))
        {
            std::cerr << "Failed to initialize WinRT capture manager" << std::endl;
            winrt_capture_manager_.reset();
            return false;
        }

        // 获取实际捕获尺寸
        capture_width_ = winrt_capture_manager_->GetWidth();
        capture_height_ = winrt_capture_manager_->GetHeight();

        std::cout << "Native WinRT capture initialized: " << capture_width_ << "x" << capture_height_ << std::endl;
        return true;
    }
    catch (...)
    {
        std::cerr << "Exception during native WinRT initialization" << std::endl;
        winrt_capture_manager_.reset();
        return false;
    }
}

bool ScreenCaptureOBSDynamic::InitializeOBSWinRT(int x, int y, int width, int height)
{
    std::cout << "Attempting OBS WinRT capture initialization..." << std::endl;

    // Load OBS libraries
    if (!LoadOBSLibraries())
    {
        std::cerr << "Failed to load OBS libraries" << std::endl;
        return false;
    }

    // Load function pointers
    if (!LoadFunctionPointers())
    {
        std::cerr << "Failed to load function pointers" << std::endl;
        return false;
    }

    // Check if WinRT capture is supported
    if (!winrt_capture_supported_ || !winrt_capture_supported_())
    {
        std::cerr << "OBS WinRT capture is not supported on this system" << std::endl;
        return false;
    }

    // List available monitors for debugging
    ListMonitors();

    if (!InitializeCapture())
    {
        return false;
    }

    std::cout << "OBS WinRT capture initialized successfully" << std::endl;
    return true;
}

bool ScreenCaptureOBSDynamic::InitializeGDICapture(int x, int y, int width, int height)
{
    std::cout << "Attempting GDI capture initialization..." << std::endl;

    // GDI捕获不需要特殊初始化，只需要设置参数
    capture_width_ = width;
    capture_height_ = height;

    // 验证屏幕区域是否有效
    HDC screen_dc = GetDC(nullptr);
    if (!screen_dc)
    {
        std::cerr << "Failed to get screen DC for GDI capture" << std::endl;
        return false;
    }

    int screen_width = GetSystemMetrics(SM_CXSCREEN);
    int screen_height = GetSystemMetrics(SM_CYSCREEN);
    ReleaseDC(nullptr, screen_dc);

    if (x + width > screen_width || y + height > screen_height)
    {
        std::cerr << "Capture region exceeds screen bounds" << std::endl;
        return false;
    }

    std::cout << "GDI capture initialized for region: " << x << "," << y << " " << width << "x" << height << std::endl;
    return true;
}

bool ScreenCaptureOBSDynamic::LoadOBSLibraries()
{
    // Load OBS WinRT DLL
    obs_winrt_dll_ = LoadLibraryA("C:\\Program Files\\obs-studio\\bin\\64bit\\libobs-winrt.dll");
    if (!obs_winrt_dll_)
    {
        std::cerr << "Failed to load libobs-winrt.dll" << std::endl;
        return false;
    }

    // Load D3D11 DLL
    d3d11_dll_ = LoadLibraryA("d3d11.dll");
    if (!d3d11_dll_)
    {
        std::cerr << "Failed to load d3d11.dll" << std::endl;
        FreeLibrary(obs_winrt_dll_);
        obs_winrt_dll_ = nullptr;
        return false;
    }

    std::cout << "OBS libraries loaded successfully" << std::endl;
    return true;
}

bool ScreenCaptureOBSDynamic::LoadFunctionPointers()
{
    // Load OBS WinRT function pointers
    winrt_capture_supported_ = (winrt_capture_supported_func)GetProcAddress(obs_winrt_dll_, "winrt_capture_supported");
    winrt_capture_init_monitor_ = (winrt_capture_init_monitor_func)GetProcAddress(obs_winrt_dll_, "winrt_capture_init_monitor");
    winrt_capture_free_ = (winrt_capture_free_func)GetProcAddress(obs_winrt_dll_, "winrt_capture_free");
    winrt_capture_active_ = (winrt_capture_active_func)GetProcAddress(obs_winrt_dll_, "winrt_capture_active");
    winrt_capture_render_ = (winrt_capture_render_func)GetProcAddress(obs_winrt_dll_, "winrt_capture_render");
    winrt_capture_width_ = (winrt_capture_width_func)GetProcAddress(obs_winrt_dll_, "winrt_capture_width");
    winrt_capture_height_ = (winrt_capture_height_func)GetProcAddress(obs_winrt_dll_, "winrt_capture_height");
    winrt_capture_thread_start_ = (winrt_capture_thread_start_func)GetProcAddress(obs_winrt_dll_, "winrt_capture_thread_start");
    winrt_capture_thread_stop_ = (winrt_capture_thread_stop_func)GetProcAddress(obs_winrt_dll_, "winrt_capture_thread_stop");

    // Load D3D11 function pointers
    d3d11_create_device_ = (d3d11_device_create_func)GetProcAddress(d3d11_dll_, "D3D11CreateDevice");
    d3d11_texture2d_get_desc_ = (d3d11_texture2d_get_desc_func)GetProcAddress(d3d11_dll_, "D3D11Texture2D_GetDesc");
    d3d11_context_map_ = (d3d11_context_map_func)GetProcAddress(d3d11_dll_, "D3D11Context_Map");
    d3d11_context_unmap_ = (d3d11_context_unmap_func)GetProcAddress(d3d11_dll_, "D3D11Context_Unmap");

    // Check if all required functions are loaded
    if (!winrt_capture_supported_ || !winrt_capture_init_monitor_ || !winrt_capture_free_ ||
        !winrt_capture_active_ || !winrt_capture_render_ || !winrt_capture_width_ ||
        !winrt_capture_height_ || !winrt_capture_thread_start_ || !winrt_capture_thread_stop_)
    {
        std::cerr << "Failed to load required OBS WinRT functions" << std::endl;
        return false;
    }

    if (!d3d11_create_device_)
    {
        std::cerr << "Failed to load required D3D11 functions" << std::endl;
        return false;
    }

    std::cout << "Function pointers loaded successfully" << std::endl;
    return true;
}

bool ScreenCaptureOBSDynamic::InitializeCapture()
{
    try
    {
        // Start WinRT capture thread
        if (winrt_capture_thread_start_)
        {
            winrt_capture_thread_start_();
        }

        // Get the monitor at the capture position
        selected_monitor_ = GetMonitorAtPosition(capture_x_, capture_y_);
        if (!selected_monitor_)
        {
            std::cout << "No monitor found at position (" << capture_x_ << ", " << capture_y_ << "), using primary monitor" << std::endl;
            selected_monitor_ = GetPrimaryMonitor();
        }

        if (!selected_monitor_)
        {
            std::cerr << "No monitor available for capture" << std::endl;
            return false;
        }

        // Initialize WinRT capture for the selected monitor
        // cursor = TRUE, monitor = selected_monitor_, force_sdr = FALSE
        capture_ = winrt_capture_init_monitor_(TRUE, selected_monitor_, FALSE);

        if (!capture_)
        {
            std::cerr << "Failed to initialize WinRT capture" << std::endl;
            return false;
        }

        if (!winrt_capture_active_(capture_))
        {
            std::cerr << "WinRT capture is not active" << std::endl;
            winrt_capture_free_(capture_);
            capture_ = nullptr;
            return false;
        }

        // Create D3D11 device for texture operations
        D3D_FEATURE_LEVEL feature_level;
        HRESULT hr = D3D11CreateDevice(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, 0,
                                       nullptr, 0, D3D11_SDK_VERSION,
                                       &d3d11_device_, &feature_level, &d3d11_context_);
        if (FAILED(hr))
        {
            std::cerr << "Failed to create D3D11 device" << std::endl;
            winrt_capture_free_(capture_);
            capture_ = nullptr;
            return false;
        }

        std::cout << "WinRT capture initialized successfully!" << std::endl;
        std::cout << "Capture dimensions: " << winrt_capture_width_(capture_) << "x" << winrt_capture_height_(capture_) << std::endl;

        return true;
    }
    catch (const std::exception &e)
    {
        std::cerr << "Exception during WinRT initialization: " << e.what() << std::endl;
        return false;
    }
}

bool ScreenCaptureOBSDynamic::CaptureFrame(std::vector<uint8_t> &frame_data, int &width, int &height)
{
    if (!initialized_)
    {
        std::cerr << "Screen capture not initialized" << std::endl;
        return false;
    }

    // 根据当前捕获模式调用相应的捕获方法
    switch (current_capture_mode_)
    {
    case CAPTURE_MODE_NATIVE_WINRT:
        return CaptureFrameNativeWinRT(frame_data, width, height);

    case CAPTURE_MODE_OBS_WINRT:
        return CaptureFrameOBSWinRT(frame_data, width, height);

    case CAPTURE_MODE_GDI:
        return CaptureFrameGDI(frame_data, width, height);

    default:
        std::cerr << "Unknown capture mode" << std::endl;
        return false;
    }
}

bool ScreenCaptureOBSDynamic::CaptureFrameNativeWinRT(std::vector<uint8_t> &frame_data, int &width, int &height)
{
    if (!winrt_capture_manager_)
    {
        std::cerr << "WinRT capture manager not available" << std::endl;
        return false;
    }

    try
    {
        return winrt_capture_manager_->CaptureFrame(frame_data, width, height);
    }
    catch (...)
    {
        std::cerr << "Exception during native WinRT frame capture" << std::endl;
        return false;
    }
}

bool ScreenCaptureOBSDynamic::CaptureFrameOBSWinRT(std::vector<uint8_t> &frame_data, int &width, int &height)
{
    if (!capture_)
    {
        std::cerr << "OBS WinRT capture not initialized" << std::endl;
        return false;
    }

    try
    {
        // Render the capture to get the latest frame
        winrt_capture_render_(capture_);

        // Get the dimensions
        width = winrt_capture_width_(capture_);
        height = winrt_capture_height_(capture_);

        if (width == 0 || height == 0)
        {
            std::cerr << "Invalid capture dimensions: " << width << "x" << height << std::endl;
            return false;
        }

        // Convert the captured texture to BGRA format (使用原有的GDI实现)
        if (!ConvertTextureToBGRA(frame_data, width, height))
        {
            std::cerr << "Failed to convert texture to BGRA" << std::endl;
            return false;
        }

        return true;
    }
    catch (const std::exception &e)
    {
        std::cerr << "Exception during OBS WinRT frame capture: " << e.what() << std::endl;
        return false;
    }
}

bool ScreenCaptureOBSDynamic::CaptureFrameGDI(std::vector<uint8_t> &frame_data, int &width, int &height)
{
    try
    {
        width = capture_width_;
        height = capture_height_;

        // 直接使用GDI进行屏幕捕获
        return ConvertTextureToBGRA(frame_data, width, height);
    }
    catch (...)
    {
        std::cerr << "Exception during GDI frame capture" << std::endl;
        return false;
    }
}

bool ScreenCaptureOBSDynamic::ConvertTextureToBGRA(std::vector<uint8_t> &frame_data, int &width, int &height)
{
    // 高性能屏幕截图实现 - 优化内存分配和像素格式转换
    int frame_size = width * height * 4; // BGRA
    frame_data.resize(frame_size);

    // 获取屏幕DC，使用更高效的方式
    HDC screen_dc = GetDC(nullptr);
    if (!screen_dc)
    {
        std::cerr << "Failed to get screen DC" << std::endl;
        return false;
    }

    // 创建内存DC
    HDC mem_dc = CreateCompatibleDC(screen_dc);
    if (!mem_dc)
    {
        std::cerr << "Failed to create compatible DC" << std::endl;
        ReleaseDC(nullptr, screen_dc);
        return false;
    }

    // 创建DIB位图以避免额外的像素格式转换
    BITMAPINFO bmi;
    ZeroMemory(&bmi, sizeof(bmi));
    bmi.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
    bmi.bmiHeader.biWidth = width;
    bmi.bmiHeader.biHeight = -height; // 负值表示自上而下，直接兼容FFmpeg
    bmi.bmiHeader.biPlanes = 1;
    bmi.bmiHeader.biBitCount = 32; // 32位BGRA格式
    bmi.bmiHeader.biCompression = BI_RGB;
    bmi.bmiHeader.biSizeImage = frame_size;

    void *bitmap_bits = nullptr;
    HBITMAP bitmap = CreateDIBSection(mem_dc, &bmi, DIB_RGB_COLORS, &bitmap_bits, nullptr, 0);
    if (!bitmap || !bitmap_bits)
    {
        std::cerr << "Failed to create DIB section" << std::endl;
        DeleteDC(mem_dc);
        ReleaseDC(nullptr, screen_dc);
        return false;
    }

    // 选择位图到内存DC
    HBITMAP old_bitmap = (HBITMAP)SelectObject(mem_dc, bitmap);

    // 使用高性能的BitBlt进行屏幕复制
    BOOL blt_result = BitBlt(mem_dc, 0, 0, width, height,
                             screen_dc, capture_x_, capture_y_, SRCCOPY);

    if (!blt_result)
    {
        std::cerr << "Failed to copy screen content" << std::endl;
        SelectObject(mem_dc, old_bitmap);
        DeleteObject(bitmap);
        DeleteDC(mem_dc);
        ReleaseDC(nullptr, screen_dc);
        return false;
    }

    // 确保绘制完成
    GdiFlush();

    // 直接从DIB获取像素数据，避免GetDIBits的额外开销
    memcpy(frame_data.data(), bitmap_bits, frame_size);

    // 清理资源
    SelectObject(mem_dc, old_bitmap);
    DeleteObject(bitmap);
    DeleteDC(mem_dc);
    ReleaseDC(nullptr, screen_dc);

    return true;
}

void ScreenCaptureOBSDynamic::ListMonitors()
{
    try
    {
        std::cout << "Available monitors:" << std::endl;

        // Enumerate monitors
        EnumDisplayMonitors(nullptr, nullptr, [](HMONITOR hMonitor, HDC hdcMonitor, LPRECT lprcMonitor, LPARAM dwData) -> BOOL
                            {
                MONITORINFOEX monitorInfo;
                monitorInfo.cbSize = sizeof(MONITORINFOEX);
                
                if (GetMonitorInfo(hMonitor, &monitorInfo)) {
                    std::cout << "  Monitor: " << monitorInfo.szDevice << std::endl;
                    std::cout << "    Position: (" << monitorInfo.rcMonitor.left << ", " << monitorInfo.rcMonitor.top 
                              << ") to (" << monitorInfo.rcMonitor.right << ", " << monitorInfo.rcMonitor.bottom << ")" << std::endl;
                    std::cout << "    Primary: " << (monitorInfo.dwFlags & MONITORINFOF_PRIMARY ? "yes" : "no") << std::endl;
                }
                return TRUE; }, 0);
    }
    catch (const std::exception &e)
    {
        std::cerr << "Failed to list monitors: " << e.what() << std::endl;
    }
}

HMONITOR ScreenCaptureOBSDynamic::GetPrimaryMonitor()
{
    return MonitorFromWindow(nullptr, MONITOR_DEFAULTTOPRIMARY);
}

HMONITOR ScreenCaptureOBSDynamic::GetMonitorAtPosition(int x, int y)
{
    POINT point = {x, y};
    return MonitorFromPoint(point, MONITOR_DEFAULTTONEAREST);
}

void ScreenCaptureOBSDynamic::Cleanup()
{
    // 清理WinRT捕获管理器
    if (winrt_capture_manager_)
    {
        winrt_capture_manager_->Cleanup();
        winrt_capture_manager_.reset();
    }

    // 清理OBS WinRT相关资源
    if (capture_)
    {
        winrt_capture_free_(capture_);
        capture_ = nullptr;
    }

    if (winrt_capture_thread_stop_)
    {
        winrt_capture_thread_stop_();
    }

    if (d3d11_context_)
    {
        d3d11_context_->Release();
        d3d11_context_ = nullptr;
    }

    if (d3d11_device_)
    {
        d3d11_device_->Release();
        d3d11_device_ = nullptr;
    }

    if (obs_winrt_dll_)
    {
        FreeLibrary(obs_winrt_dll_);
        obs_winrt_dll_ = nullptr;
    }

    if (d3d11_dll_)
    {
        FreeLibrary(d3d11_dll_);
        d3d11_dll_ = nullptr;
    }

    initialized_ = false;
    current_capture_mode_ = CAPTURE_MODE_NATIVE_WINRT; // 重置为默认模式
}