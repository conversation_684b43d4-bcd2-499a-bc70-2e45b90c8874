# 高性能屏幕采集推流系统

基于OBS Studio动态加载和FFmpeg的高性能屏幕采集推流系统，支持高质量H.264编码和RTMP推流。

## 主要特性

- **高性能屏幕采集**: 使用OBS Studio的WinRT-Capture技术，支持多显示器
- **高质量编码**: H.264 High Profile，2Mbps码率，CRF 18质量
- **低延迟推流**: RTMP协议，支持网络重连和错误恢复
- **动态加载**: 运行时加载OBS Studio库，无需静态链接

## 技术栈

- **屏幕采集**: OBS Studio WinRT-Capture (动态加载)
- **视频编码**: FFmpeg libx264 (H.264 High Profile)
- **推流协议**: RTMP over TCP
- **构建系统**: CMake + vcpkg
- **开发语言**: C++20

## 项目结构

```
ffmpeg_push/
├── src/                          # 源代码
│   ├── main_obs_dynamic.cpp      # 主程序入口
│   ├── screen_capture_obs_dynamic.cpp  # OBS动态加载屏幕采集
│   └── stream_pusher.cpp         # FFmpeg推流实现
├── include/                      # 头文件
│   ├── screen_capture_obs_dynamic.h
│   └── stream_pusher.h
├── deps/                         # 依赖库
│   └── libobs-winrt/            # OBS WinRT头文件
├── build/                        # 构建输出
├── redist_desk/                  # 发布目录
└── CMakeLists.txt               # CMake配置
```

## 编译要求

- **编译器**: Visual Studio 2022 (MSVC)
- **C++标准**: C++20
- **包管理器**: vcpkg
- **依赖库**: 
  - FFmpeg (libavcodec, libavformat, libavutil, libswscale)
  - OpenCV
  - OBS Studio (运行时动态加载)

## 编译步骤

1. **安装依赖**:
   ```bash
   vcpkg install ffmpeg:x64-windows
   vcpkg install opencv:x64-windows
   ```

2. **配置环境变量**:
   ```bash
   set VCPKG_ROOT=C:\dev\vcpkg
   ```

3. **编译项目**:
   ```bash
   mkdir build
   cd build
   cmake .. -G "Visual Studio 17 2022" -A x64
   cmake --build . --config Release
   ```

## 使用方法

### 基本用法

```bash
screen_capture_stream.exe --x 0 --y 0 --width 1024 --height 768 --fps 30 --url rtmp://server:1935/live/stream
```

### 参数说明

- `--x, --y`: 采集区域左上角坐标
- `--width, --height`: 采集区域尺寸
- `--fps`: 帧率 (默认25fps)
- `--url`: RTMP推流地址

### 编码参数

- **编码器**: libx264 (H.264 High Profile)
- **码率**: 2Mbps (1024x768)
- **质量**: CRF 18 (高质量)
- **预设**: medium (平衡质量和速度)
- **GOP**: 2秒 (60帧@30fps)

## 性能特点

- **高质量**: 2Mbps码率 + High Profile + CRF 18
- **低延迟**: 无B帧 + 快速预设
- **稳定性**: 网络重连 + 错误恢复
- **兼容性**: 支持SRS、Nginx-RTMP等服务器

## 依赖说明

### OBS Studio动态加载

程序会从以下路径动态加载OBS库：
- `C:\Program Files\obs-studio\bin\64bit\libobs-winrt.dll`
- `C:\Program Files\obs-studio\bin\64bit\libobs-d3d11.dll`

### FFmpeg库

通过vcpkg安装的FFmpeg库：
- `avcodec-61.dll`
- `avformat-61.dll`
- `avutil-59.dll`
- `swscale-8.dll`
- `swresample-5.dll`

## 故障排除

### 常见问题

1. **OBS库加载失败**: 确保OBS Studio已安装
2. **网络连接失败**: 检查RTMP服务器状态和网络连接
3. **编码器初始化失败**: 检查FFmpeg库版本兼容性

### 调试信息

程序会输出详细的初始化和运行信息：
- 屏幕采集初始化状态
- 编码器参数配置
- 网络连接状态
- 性能统计信息

## 许可证

本项目基于MIT许可证开源。 