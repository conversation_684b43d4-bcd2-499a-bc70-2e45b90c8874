#include <iostream>
#include <string>
#include <vector>
#include <chrono>
#include <thread>
#include <signal.h>
#include <atomic>
#include <numeric>
#include <algorithm>
#include <iomanip>

#include "screen_capture_obs_dynamic.h"
#include "stream_pusher.h"

// 全局变量用于信号处理
std::atomic<bool> g_running(true);

void SignalHandler(int signal)
{
    if (signal == SIGINT || signal == SIGTERM)
    {
        std::cout << "\n收到停止信号，正在关闭..." << std::endl;
        g_running = false;
    }
}

void PrintUsage(const char *program_name)
{
    std::cout << "Usage: " << program_name << " [options]" << std::endl;
    std::cout << "Options:" << std::endl;
    std::cout << "  --x <x>           Capture region X coordinate (default: 0)" << std::endl;
    std::cout << "  --y <y>           Capture region Y coordinate (default: 0)" << std::endl;
    std::cout << "  --width <width>   Capture region width (default: 1280)" << std::endl;
    std::cout << "  --height <height> Capture region height (default: 720)" << std::endl;
    std::cout << "  --fps <fps>       Frame rate (default: 25)" << std::endl;
    std::cout << "  --url <url>       Stream URL (default: rtmp://localhost:1935/live/stream)" << std::endl;
    std::cout << "  --help            Show this help message" << std::endl;
}

int main(int argc, char *argv[])
{
    // 设置信号处理
    signal(SIGINT, SignalHandler);
    signal(SIGTERM, SignalHandler);

    // Default parameters
    int x = 0;
    int y = 0;
    int width = 1280;
    int height = 720;
    int fps = 25;
    std::string url = "rtmp://localhost:1935/live/stream";

    // Parse command line arguments
    for (int i = 1; i < argc; i++)
    {
        std::string arg = argv[i];

        if (arg == "--help")
        {
            PrintUsage(argv[0]);
            return 0;
        }
        else if (arg == "--x" && i + 1 < argc)
        {
            x = std::stoi(argv[++i]);
        }
        else if (arg == "--y" && i + 1 < argc)
        {
            y = std::stoi(argv[++i]);
        }
        else if (arg == "--width" && i + 1 < argc)
        {
            width = std::stoi(argv[++i]);
        }
        else if (arg == "--height" && i + 1 < argc)
        {
            height = std::stoi(argv[++i]);
        }
        else if (arg == "--fps" && i + 1 < argc)
        {
            fps = std::stoi(argv[++i]);
        }
        else if (arg == "--url" && i + 1 < argc)
        {
            url = argv[++i];
        }
    }

    // Print settings
    std::cout << "High Performance Screen Capture Stream Settings:" << std::endl;
    std::cout << "  Capture Region: (" << x << ", " << y << ") " << width << "x" << height << std::endl;
    std::cout << "  Frame Rate: " << fps << " fps" << std::endl;
    std::cout << "  Stream URL: " << url << std::endl;
    std::cout << "  Using: WinRT-Capture (OBS Studio High Performance Multi-Monitor Support)" << std::endl;
    std::cout << std::endl;

    // Initialize screen capture
    std::cout << "Initializing screen capture..." << std::endl;
    ScreenCaptureOBSDynamic screen_capture;
    if (!screen_capture.Initialize(x, y, width, height))
    {
        std::cerr << "Failed to initialize screen capture" << std::endl;
        return 1;
    }

    // Initialize stream pusher
    StreamPusher stream_pusher;
    if (!stream_pusher.Initialize(url, screen_capture.GetWidth(), screen_capture.GetHeight(), fps))
    {
        std::cerr << "Failed to initialize stream pusher" << std::endl;
        return 1;
    }

    std::cout << "Starting screen capture and streaming..." << std::endl;
    std::cout << "Press Ctrl+C to stop" << std::endl;

    // Performance monitoring variables - 增强版本
    auto start_time = std::chrono::high_resolution_clock::now();
    auto last_stats_time = start_time;
    auto last_quality_check_time = start_time;
    int frame_count = 0;
    int successful_frames = 0;
    int failed_frames = 0;
    
    // 性能指标跟踪
    std::vector<double> frame_times;    // 帧处理时间
    std::vector<double> encoding_times; // 编码时间
    frame_times.reserve(1000);         // 预分配避免动态扩容
    encoding_times.reserve(1000);
    
    // 质量监控参数
    int consecutive_failures = 0;
    const int max_consecutive_failures = 5;
    bool quality_degraded = false;

    // Main capture and streaming loop
    auto frame_interval = std::chrono::microseconds(1000000 / fps);
    auto last_frame_time = std::chrono::high_resolution_clock::now();

    while (g_running)
    {
        auto current_time = std::chrono::high_resolution_clock::now();
        auto elapsed = current_time - last_frame_time;

        if (elapsed >= frame_interval)
        {
            auto frame_start_time = std::chrono::high_resolution_clock::now();
            
            std::vector<uint8_t> frame_data;
            int frame_width, frame_height;

            if (screen_capture.CaptureFrame(frame_data, frame_width, frame_height))
            {
                auto capture_end_time = std::chrono::high_resolution_clock::now();
                
                if (stream_pusher.PushFrame(frame_data, frame_width, frame_height))
                {
                    auto encoding_end_time = std::chrono::high_resolution_clock::now();
                    
                    // 记录性能指标
                    auto capture_time = std::chrono::duration<double, std::milli>(capture_end_time - frame_start_time).count();
                    auto encoding_time = std::chrono::duration<double, std::milli>(encoding_end_time - capture_end_time).count();
                    
                    frame_times.push_back(capture_time);
                    encoding_times.push_back(encoding_time);
                    
                    // 保持数组大小在合理范围内
                    if (frame_times.size() > 1000) {
                        frame_times.erase(frame_times.begin(), frame_times.begin() + 500);
                        encoding_times.erase(encoding_times.begin(), encoding_times.begin() + 500);
                    }
                    
                    successful_frames++;
                    consecutive_failures = 0; // 重置连续失败计数
                }
                else
                {
                    failed_frames++;
                    consecutive_failures++;
                    std::cerr << "Failed to push frame (consecutive failures: " << consecutive_failures << ")" << std::endl;
                    
                    // 连续失败检测
                    if (consecutive_failures >= max_consecutive_failures && !quality_degraded)
                    {
                        std::cout << "Warning: Multiple consecutive encoding failures detected. Quality may be degraded." << std::endl;
                        quality_degraded = true;
                    }
                }
            }
            else
            {
                failed_frames++;
                consecutive_failures++;
                std::cerr << "Failed to capture frame (consecutive failures: " << consecutive_failures << ")" << std::endl;
            }

            frame_count++;
            last_frame_time = current_time;

            // 增强的性能统计 - 每5秒显示详细指标
            auto stats_elapsed = current_time - last_stats_time;
            if (stats_elapsed >= std::chrono::seconds(5))
            {
                auto total_elapsed = current_time - start_time;
                double total_seconds = std::chrono::duration<double>(total_elapsed).count();
                double actual_fps = frame_count / total_seconds;
                double success_rate = (successful_frames * 100.0) / frame_count;

                // 计算性能指标
                double avg_capture_time = 0.0, avg_encoding_time = 0.0;
                double max_capture_time = 0.0, max_encoding_time = 0.0;
                
                if (!frame_times.empty()) {
                    avg_capture_time = std::accumulate(frame_times.begin(), frame_times.end(), 0.0) / frame_times.size();
                    max_capture_time = *std::max_element(frame_times.begin(), frame_times.end());
                }
                
                if (!encoding_times.empty()) {
                    avg_encoding_time = std::accumulate(encoding_times.begin(), encoding_times.end(), 0.0) / encoding_times.size();
                    max_encoding_time = *std::max_element(encoding_times.begin(), encoding_times.end());
                }

                std::cout << "=== Enhanced Performance Stats ===" << std::endl;
                std::cout << "  Total Frames: " << frame_count << std::endl;
                std::cout << "  Successful: " << successful_frames << " (" << success_rate << "%)" << std::endl;
                std::cout << "  Failed: " << failed_frames << std::endl;
                std::cout << "  Target FPS: " << fps << " | Actual FPS: " << std::fixed << std::setprecision(2) << actual_fps << std::endl;
                std::cout << "  Capture Time: avg=" << std::setprecision(1) << avg_capture_time << "ms, max=" << max_capture_time << "ms" << std::endl;
                std::cout << "  Encoding Time: avg=" << avg_encoding_time << "ms, max=" << max_encoding_time << "ms" << std::endl;
                std::cout << "  Total Processing: avg=" << (avg_capture_time + avg_encoding_time) << "ms" << std::endl;
                std::cout << "  Running Time: " << std::setprecision(1) << total_seconds << "s" << std::endl;
                
                // 性能警告
                if (avg_capture_time + avg_encoding_time > (1000.0 / fps * 0.8)) {
                    std::cout << "  ⚠️  Warning: Processing time approaching frame interval limit!" << std::endl;
                }
                if (actual_fps < fps * 0.95) {
                    std::cout << "  ⚠️  Warning: Frame rate below target!" << std::endl;
                }
                if (quality_degraded) {
                    std::cout << "  ⚠️  Warning: Quality degradation detected!" << std::endl;
                }
                
                std::cout << std::endl;
                last_stats_time = current_time;
            }
        }
        else
        {
            // Sleep for a short time to avoid busy waiting
            std::this_thread::sleep_for(std::chrono::microseconds(1000));
        }
    }

    // Final statistics
    auto end_time = std::chrono::high_resolution_clock::now();
    auto total_elapsed = end_time - start_time;
    double total_seconds = std::chrono::duration<double>(total_elapsed).count();
    double final_fps = frame_count / total_seconds;
    double final_success_rate = (successful_frames * 100.0) / frame_count;

    std::cout << "\n=== Final Statistics ===" << std::endl;
    std::cout << "Total Runtime: " << total_seconds << " seconds" << std::endl;
    std::cout << "Total Frames: " << frame_count << std::endl;
    std::cout << "Successful Frames: " << successful_frames << std::endl;
    std::cout << "Failed Frames: " << failed_frames << std::endl;
    std::cout << "Average FPS: " << final_fps << std::endl;
    std::cout << "Success Rate: " << final_success_rate << "%" << std::endl;
    std::cout << "Streaming stopped" << std::endl;

    return 0;
}