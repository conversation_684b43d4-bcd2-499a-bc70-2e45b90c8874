﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{6E058D59-2A74-3996-9051-7F9981A13FB1}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>screen_capture_stream</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\bin\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">screen_capture_stream.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">screen_capture_stream_v2</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\bin\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">screen_capture_stream.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">screen_capture_stream_v2</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\bin\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">screen_capture_stream.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">screen_capture_stream_v2</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\bin\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">screen_capture_stream.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">screen_capture_stream_v2</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\dev\nuaa\push_video_stream_server\ffmpeg_push\include;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\deps\libobs-winrt;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/dev/vcpkg/installed/x64-windows/include/opencv4"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\dev\nuaa\push_video_stream_server\ffmpeg_push\include;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\deps\libobs-winrt;C:\dev\vcpkg\installed\x64-windows\include;C:\dev\vcpkg\installed\x64-windows\include\opencv4;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\dev\nuaa\push_video_stream_server\ffmpeg_push\include;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\deps\libobs-winrt;C:\dev\vcpkg\installed\x64-windows\include;C:\dev\vcpkg\installed\x64-windows\include\opencv4;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>C:\dev\vcpkg\installed\x64-windows\lib\avcodec.lib;C:\dev\vcpkg\installed\x64-windows\lib\avformat.lib;C:\dev\vcpkg\installed\x64-windows\lib\avutil.lib;C:\dev\vcpkg\installed\x64-windows\lib\swscale.lib;C:\dev\vcpkg\installed\x64-windows\lib\swresample.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\opencv_highgui4d.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\opencv_ml4d.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\opencv_objdetect4d.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\opencv_photo4d.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\opencv_stitching4d.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\opencv_video4d.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\opencv_videoio4d.lib;gdi32.lib;user32.lib;kernel32.lib;d3d11.lib;dxgi.lib;dwmapi.lib;windowsapp.lib;d3dcompiler.lib;version.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\opencv_imgcodecs4d.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\opencv_calib3d4d.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\opencv_dnn4d.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\opencv_features2d4d.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\opencv_flann4d.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\opencv_imgproc4d.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\opencv_core4d.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/dev/nuaa/push_video_stream_server/ffmpeg_push/build/Debug/screen_capture_stream_v2.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/dev/nuaa/push_video_stream_server/ffmpeg_push/build/bin/Debug/screen_capture_stream_v2.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\dev\nuaa\push_video_stream_server\ffmpeg_push\include;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\deps\libobs-winrt;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/dev/vcpkg/installed/x64-windows/include/opencv4"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\dev\nuaa\push_video_stream_server\ffmpeg_push\include;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\deps\libobs-winrt;C:\dev\vcpkg\installed\x64-windows\include;C:\dev\vcpkg\installed\x64-windows\include\opencv4;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\dev\nuaa\push_video_stream_server\ffmpeg_push\include;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\deps\libobs-winrt;C:\dev\vcpkg\installed\x64-windows\include;C:\dev\vcpkg\installed\x64-windows\include\opencv4;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>C:\dev\vcpkg\installed\x64-windows\lib\avcodec.lib;C:\dev\vcpkg\installed\x64-windows\lib\avformat.lib;C:\dev\vcpkg\installed\x64-windows\lib\avutil.lib;C:\dev\vcpkg\installed\x64-windows\lib\swscale.lib;C:\dev\vcpkg\installed\x64-windows\lib\swresample.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_highgui4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_ml4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_objdetect4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_photo4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_stitching4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_video4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_videoio4.lib;gdi32.lib;user32.lib;kernel32.lib;d3d11.lib;dxgi.lib;dwmapi.lib;windowsapp.lib;d3dcompiler.lib;version.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_imgcodecs4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_calib3d4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_dnn4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_features2d4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_flann4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_imgproc4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_core4.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/dev/nuaa/push_video_stream_server/ffmpeg_push/build/Release/screen_capture_stream_v2.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/dev/nuaa/push_video_stream_server/ffmpeg_push/build/bin/Release/screen_capture_stream_v2.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\dev\nuaa\push_video_stream_server\ffmpeg_push\include;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\deps\libobs-winrt;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/dev/vcpkg/installed/x64-windows/include/opencv4"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\dev\nuaa\push_video_stream_server\ffmpeg_push\include;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\deps\libobs-winrt;C:\dev\vcpkg\installed\x64-windows\include;C:\dev\vcpkg\installed\x64-windows\include\opencv4;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\dev\nuaa\push_video_stream_server\ffmpeg_push\include;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\deps\libobs-winrt;C:\dev\vcpkg\installed\x64-windows\include;C:\dev\vcpkg\installed\x64-windows\include\opencv4;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>C:\dev\vcpkg\installed\x64-windows\lib\avcodec.lib;C:\dev\vcpkg\installed\x64-windows\lib\avformat.lib;C:\dev\vcpkg\installed\x64-windows\lib\avutil.lib;C:\dev\vcpkg\installed\x64-windows\lib\swscale.lib;C:\dev\vcpkg\installed\x64-windows\lib\swresample.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_highgui4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_ml4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_objdetect4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_photo4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_stitching4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_video4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_videoio4.lib;gdi32.lib;user32.lib;kernel32.lib;d3d11.lib;dxgi.lib;dwmapi.lib;windowsapp.lib;d3dcompiler.lib;version.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_imgcodecs4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_calib3d4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_dnn4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_features2d4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_flann4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_imgproc4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_core4.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/dev/nuaa/push_video_stream_server/ffmpeg_push/build/MinSizeRel/screen_capture_stream_v2.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/dev/nuaa/push_video_stream_server/ffmpeg_push/build/bin/MinSizeRel/screen_capture_stream_v2.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\dev\nuaa\push_video_stream_server\ffmpeg_push\include;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\deps\libobs-winrt;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/dev/vcpkg/installed/x64-windows/include/opencv4"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\dev\nuaa\push_video_stream_server\ffmpeg_push\include;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\deps\libobs-winrt;C:\dev\vcpkg\installed\x64-windows\include;C:\dev\vcpkg\installed\x64-windows\include\opencv4;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\dev\nuaa\push_video_stream_server\ffmpeg_push\include;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\deps\libobs-winrt;C:\dev\vcpkg\installed\x64-windows\include;C:\dev\vcpkg\installed\x64-windows\include\opencv4;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>C:\dev\vcpkg\installed\x64-windows\lib\avcodec.lib;C:\dev\vcpkg\installed\x64-windows\lib\avformat.lib;C:\dev\vcpkg\installed\x64-windows\lib\avutil.lib;C:\dev\vcpkg\installed\x64-windows\lib\swscale.lib;C:\dev\vcpkg\installed\x64-windows\lib\swresample.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_highgui4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_ml4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_objdetect4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_photo4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_stitching4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_video4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_videoio4.lib;gdi32.lib;user32.lib;kernel32.lib;d3d11.lib;dxgi.lib;dwmapi.lib;windowsapp.lib;d3dcompiler.lib;version.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_imgcodecs4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_calib3d4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_dnn4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_features2d4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_flann4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_imgproc4.lib;C:\dev\vcpkg\installed\x64-windows\lib\opencv_core4.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/dev/nuaa/push_video_stream_server/ffmpeg_push/build/RelWithDebInfo/screen_capture_stream_v2.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/dev/nuaa/push_video_stream_server/ffmpeg_push/build/bin/RelWithDebInfo/screen_capture_stream_v2.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\dev\nuaa\push_video_stream_server\ffmpeg_push\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/dev/nuaa/push_video_stream_server/ffmpeg_push/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/dev/nuaa/push_video_stream_server/ffmpeg_push -BC:/dev/nuaa/push_video_stream_server/ffmpeg_push/build --check-stamp-file C:/dev/nuaa/push_video_stream_server/ffmpeg_push/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindTIFF.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeCCompiler.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeSystem.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVConfig-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVModules-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVModules-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVModules.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-generate.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-module.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-options.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\quirc\quirc-config-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\quirc\quirc-config-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\quirc\quirc-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-targets.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/dev/nuaa/push_video_stream_server/ffmpeg_push/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/dev/nuaa/push_video_stream_server/ffmpeg_push -BC:/dev/nuaa/push_video_stream_server/ffmpeg_push/build --check-stamp-file C:/dev/nuaa/push_video_stream_server/ffmpeg_push/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindTIFF.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeCCompiler.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeSystem.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVConfig-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVModules-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVModules-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVModules.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-generate.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-module.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-options.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\quirc\quirc-config-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\quirc\quirc-config-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\quirc\quirc-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-targets.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/dev/nuaa/push_video_stream_server/ffmpeg_push/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/dev/nuaa/push_video_stream_server/ffmpeg_push -BC:/dev/nuaa/push_video_stream_server/ffmpeg_push/build --check-stamp-file C:/dev/nuaa/push_video_stream_server/ffmpeg_push/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindTIFF.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeCCompiler.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeSystem.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVConfig-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVModules-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVModules-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVModules.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-generate.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-module.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-options.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\quirc\quirc-config-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\quirc\quirc-config-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\quirc\quirc-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-targets.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/dev/nuaa/push_video_stream_server/ffmpeg_push/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/dev/nuaa/push_video_stream_server/ffmpeg_push -BC:/dev/nuaa/push_video_stream_server/ffmpeg_push/build --check-stamp-file C:/dev/nuaa/push_video_stream_server/ffmpeg_push/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindTIFF.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeCCompiler.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\3.26.4\CMakeSystem.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\absl\abslTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVConfig-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVModules-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVModules-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\opencv4\OpenCVModules.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-generate.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-module.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-options.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\protobuf\protobuf-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\quirc\quirc-config-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\quirc\quirc-config-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\quirc\quirc-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\utf8_range\utf8_range-targets.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C:\dev\nuaa\push_video_stream_server\ffmpeg_push\src\main_obs_dynamic.cpp" />
    <ClCompile Include="C:\dev\nuaa\push_video_stream_server\ffmpeg_push\src\screen_capture_obs_dynamic.cpp" />
    <ClCompile Include="C:\dev\nuaa\push_video_stream_server\ffmpeg_push\src\stream_pusher.cpp" />
    <ClCompile Include="C:\dev\nuaa\push_video_stream_server\ffmpeg_push\src\winrt_capture_manager.cpp" />
    <ClCompile Include="C:\dev\nuaa\push_video_stream_server\ffmpeg_push\src\hardware_encoder_manager.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="C:\dev\nuaa\push_video_stream_server\ffmpeg_push\build\ZERO_CHECK.vcxproj">
      <Project>{CB5C35AC-EC70-3D24-83A1-EE63B6C2AE29}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>