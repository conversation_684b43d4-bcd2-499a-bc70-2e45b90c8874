#include "hardware_encoder_manager.h"
#include <iostream>
#include <algorithm>
#include <sstream>

extern "C"
{
#include <libswscale/swscale.h>
#include <libavutil/imgutils.h>
}

HardwareEncoderManager::HardwareEncoderManager()
    : encoders_detected_(false)
{
    metrics_.Reset();
}

HardwareEncoderManager::~HardwareEncoderManager()
{
}

std::vector<HardwareEncoderManager::EncoderInfo> HardwareEncoderManager::DetectAvailableEncoders()
{
    if (encoders_detected_)
    {
        return available_encoders_;
    }

    available_encoders_.clear();

    std::cout << "Detecting available hardware encoders..." << std::endl;

    // 检测软件编码器 (总是可用)
    if (DetectSoftwareEncoder())
    {
        std::cout << "✅ Software encoder (libx264) available" << std::endl;
    }

    // 检测NVENC
    if (DetectNVENC())
    {
        std::cout << "✅ NVENC hardware encoder available" << std::endl;
    }
    else
    {
        std::cout << "❌ NVENC hardware encoder not available" << std::endl;
    }

    // 检测AMF
    if (DetectAMF())
    {
        std::cout << "✅ AMF hardware encoder available" << std::endl;
    }
    else
    {
        std::cout << "❌ AMF hardware encoder not available" << std::endl;
    }

    // 检测QSV
    if (DetectQSV())
    {
        std::cout << "✅ QSV hardware encoder available" << std::endl;
    }
    else
    {
        std::cout << "❌ QSV hardware encoder not available" << std::endl;
    }

    // 按优先级排序
    std::sort(available_encoders_.begin(), available_encoders_.end(),
              [](const EncoderInfo &a, const EncoderInfo &b)
              {
                  return a.priority < b.priority;
              });

    encoders_detected_ = true;

    std::cout << "Encoder detection completed. Available encoders: " << available_encoders_.size() << std::endl;
    return available_encoders_;
}

bool HardwareEncoderManager::DetectNVENC()
{
    const AVCodec *codec = avcodec_find_encoder_by_name("h264_nvenc");
    if (!codec)
    {
        return false;
    }

    // 尝试创建编码器上下文进行测试
    if (!TestEncoder(codec, 1920, 1080))
    {
        return false;
    }

    EncoderInfo info;
    info.type = ENCODER_NVENC;
    info.name = "h264_nvenc";
    info.description = "NVIDIA NVENC Hardware Encoder";
    info.available = true;
    info.priority = 1; // 最高优先级
    info.max_width = 4096;
    info.max_height = 4096;
    info.supports_bframes = true;
    info.supports_cabac = true;

    available_encoders_.push_back(info);
    return true;
}

bool HardwareEncoderManager::DetectAMF()
{
    const AVCodec *codec = avcodec_find_encoder_by_name("h264_amf");
    if (!codec)
    {
        return false;
    }

    if (!TestEncoder(codec, 1920, 1080))
    {
        return false;
    }

    EncoderInfo info;
    info.type = ENCODER_AMF;
    info.name = "h264_amf";
    info.description = "AMD AMF Hardware Encoder";
    info.available = true;
    info.priority = 2;
    info.max_width = 4096;
    info.max_height = 4096;
    info.supports_bframes = true;
    info.supports_cabac = true;

    available_encoders_.push_back(info);
    return true;
}

bool HardwareEncoderManager::DetectQSV()
{
    const AVCodec *codec = avcodec_find_encoder_by_name("h264_qsv");
    if (!codec)
    {
        return false;
    }

    if (!TestEncoder(codec, 1920, 1080))
    {
        return false;
    }

    EncoderInfo info;
    info.type = ENCODER_QSV;
    info.name = "h264_qsv";
    info.description = "Intel Quick Sync Video Hardware Encoder";
    info.available = true;
    info.priority = 3;
    info.max_width = 4096;
    info.max_height = 4096;
    info.supports_bframes = true;
    info.supports_cabac = true;

    available_encoders_.push_back(info);
    return true;
}

bool HardwareEncoderManager::DetectSoftwareEncoder()
{
    const AVCodec *codec = avcodec_find_encoder_by_name("libx264");
    if (!codec)
    {
        return false;
    }

    EncoderInfo info;
    info.type = ENCODER_SOFTWARE;
    info.name = "libx264";
    info.description = "Software H.264 Encoder";
    info.available = true;
    info.priority = 10; // 最低优先级
    info.max_width = 8192;
    info.max_height = 8192;
    info.supports_bframes = true;
    info.supports_cabac = true;

    available_encoders_.push_back(info);
    return true;
}

bool HardwareEncoderManager::TestEncoder(const AVCodec *codec, int width, int height)
{
    AVCodecContext *test_ctx = avcodec_alloc_context3(codec);
    if (!test_ctx)
    {
        return false;
    }

    // 设置基本参数
    test_ctx->width = width;
    test_ctx->height = height;
    test_ctx->time_base = {1, 30};
    test_ctx->framerate = {30, 1};
    test_ctx->bit_rate = 2000000;
    test_ctx->gop_size = 60;
    test_ctx->max_b_frames = 0;

    // 设置像素格式
    if (codec->pix_fmts)
    {
        test_ctx->pix_fmt = codec->pix_fmts[0];
    }
    else
    {
        test_ctx->pix_fmt = AV_PIX_FMT_YUV420P;
    }

    // 尝试打开编码器
    int ret = avcodec_open2(test_ctx, codec, nullptr);
    bool success = (ret >= 0);

    // 清理
    avcodec_free_context(&test_ctx);

    return success;
}

HardwareEncoderManager::EncoderType HardwareEncoderManager::SelectBestEncoder(
    int width, int height, int fps, bool prefer_quality)
{
    if (!encoders_detected_)
    {
        DetectAvailableEncoders();
    }

    if (available_encoders_.empty())
    {
        std::cerr << "No encoders available" << std::endl;
        return ENCODER_UNKNOWN;
    }

    // 过滤支持当前分辨率的编码器
    std::vector<EncoderInfo> suitable_encoders;
    for (const auto &encoder : available_encoders_)
    {
        if (encoder.available &&
            width <= encoder.max_width &&
            height <= encoder.max_height)
        {
            suitable_encoders.push_back(encoder);
        }
    }

    if (suitable_encoders.empty())
    {
        std::cerr << "No suitable encoders for resolution " << width << "x" << height << std::endl;
        return ENCODER_UNKNOWN;
    }

    // 选择策略
    if (prefer_quality)
    {
        // 优先质量: 软件编码器 > NVENC > AMF > QSV
        for (const auto &encoder : suitable_encoders)
        {
            if (encoder.type == ENCODER_SOFTWARE)
                return ENCODER_SOFTWARE;
        }
        for (const auto &encoder : suitable_encoders)
        {
            if (encoder.type == ENCODER_NVENC)
                return ENCODER_NVENC;
        }
        for (const auto &encoder : suitable_encoders)
        {
            if (encoder.type == ENCODER_AMF)
                return ENCODER_AMF;
        }
        for (const auto &encoder : suitable_encoders)
        {
            if (encoder.type == ENCODER_QSV)
                return ENCODER_QSV;
        }
    }
    else
    {
        // 优先性能: 按priority排序，硬件编码器优先
        for (const auto &encoder : suitable_encoders)
        {
            if (encoder.type != ENCODER_SOFTWARE)
            {
                return encoder.type;
            }
        }
        // 如果没有硬件编码器，使用软件编码器
        return ENCODER_SOFTWARE;
    }

    // 默认返回第一个可用的编码器
    return suitable_encoders[0].type;
}

std::string HardwareEncoderManager::GetEncoderName(EncoderType type)
{
    switch (type)
    {
    case ENCODER_SOFTWARE:
        return "libx264";
    case ENCODER_NVENC:
        return "h264_nvenc";
    case ENCODER_AMF:
        return "h264_amf";
    case ENCODER_QSV:
        return "h264_qsv";
    default:
        return "unknown";
    }
}

std::string HardwareEncoderManager::GetEncoderDescription(EncoderType type)
{
    switch (type)
    {
    case ENCODER_SOFTWARE:
        return "Software H.264 Encoder (libx264)";
    case ENCODER_NVENC:
        return "NVIDIA NVENC Hardware Encoder";
    case ENCODER_AMF:
        return "AMD AMF Hardware Encoder";
    case ENCODER_QSV:
        return "Intel Quick Sync Video Hardware Encoder";
    default:
        return "Unknown Encoder";
    }
}

bool HardwareEncoderManager::IsEncoderAvailable(EncoderType type)
{
    if (!encoders_detected_)
    {
        DetectAvailableEncoders();
    }

    for (const auto &encoder : available_encoders_)
    {
        if (encoder.type == type && encoder.available)
        {
            return true;
        }
    }
    return false;
}

void HardwareEncoderManager::UpdateMetrics(double encoding_time, bool success)
{
    if (success)
    {
        metrics_.successful_frames++;
        metrics_.UpdateMetrics(encoding_time);
    }
    else
    {
        metrics_.failed_frames++;
    }
}

void HardwareEncoderManager::SetCurrentEncoder(EncoderType type)
{
    metrics_.current_encoder = type;
    metrics_.encoder_name = GetEncoderName(type);
}

AVCodecContext *HardwareEncoderManager::CreateEncoderContext(EncoderType type, int width, int height, int fps, int bitrate)
{
    const AVCodec *codec = nullptr;

    // 查找编码器
    switch (type)
    {
    case ENCODER_NVENC:
        codec = avcodec_find_encoder_by_name("h264_nvenc");
        break;
    case ENCODER_AMF:
        codec = avcodec_find_encoder_by_name("h264_amf");
        break;
    case ENCODER_QSV:
        codec = avcodec_find_encoder_by_name("h264_qsv");
        break;
    case ENCODER_SOFTWARE:
        codec = avcodec_find_encoder_by_name("libx264");
        break;
    default:
        std::cerr << "Unknown encoder type: " << type << std::endl;
        return nullptr;
    }

    if (!codec)
    {
        std::cerr << "Failed to find encoder: " << GetEncoderName(type) << std::endl;
        return nullptr;
    }

    // 创建编码器上下文
    AVCodecContext *ctx = avcodec_alloc_context3(codec);
    if (!ctx)
    {
        std::cerr << "Failed to allocate codec context" << std::endl;
        return nullptr;
    }

    // 计算码率
    if (bitrate == 0)
    {
        bitrate = CalculateOptimalBitrate(width, height, fps, type);
    }

    // 设置基本参数
    ctx->width = width;
    ctx->height = height;
    ctx->time_base = {1, fps};
    ctx->framerate = {fps, 1};
    ctx->bit_rate = bitrate;
    ctx->gop_size = fps * 2; // 2秒GOP
    ctx->max_b_frames = 0;   // 低延迟，不使用B帧
    ctx->pix_fmt = GetPreferredPixelFormat(type);

    // 根据编码器类型进行特定配置
    bool config_success = false;
    switch (type)
    {
    case ENCODER_NVENC:
        config_success = ConfigureNVENC(ctx, width, height, fps, bitrate);
        break;
    case ENCODER_AMF:
        config_success = ConfigureAMF(ctx, width, height, fps, bitrate);
        break;
    case ENCODER_QSV:
        config_success = ConfigureQSV(ctx, width, height, fps, bitrate);
        break;
    case ENCODER_SOFTWARE:
        config_success = ConfigureSoftware(ctx, width, height, fps, bitrate);
        break;
    default:
        config_success = false;
    }

    if (!config_success)
    {
        std::cerr << "Failed to configure encoder: " << GetEncoderName(type) << std::endl;
        avcodec_free_context(&ctx);
        return nullptr;
    }

    std::cout << "Created encoder context: " << GetEncoderName(type)
              << " (" << width << "x" << height << "@" << fps << "fps, "
              << bitrate / 1000 << "kbps)" << std::endl;

    return ctx;
}

bool HardwareEncoderManager::ConfigureNVENC(AVCodecContext *ctx, int width, int height, int fps, int bitrate)
{
    try
    {
        // NVENC特定参数
        av_opt_set(ctx->priv_data, "preset", "p4", 0);        // 平衡质量和速度 (p1=fastest, p7=slowest)
        av_opt_set(ctx->priv_data, "tune", "ll", 0);          // 低延迟调优
        av_opt_set(ctx->priv_data, "rc", "cbr", 0);           // 恒定码率模式
        av_opt_set(ctx->priv_data, "profile", "high", 0);     // High Profile
        av_opt_set_int(ctx->priv_data, "zerolatency", 1, 0);  // 零延迟模式
        av_opt_set_int(ctx->priv_data, "delay", 0, 0);        // 无延迟
        av_opt_set_int(ctx->priv_data, "rc-lookahead", 0, 0); // 禁用前瞻
        av_opt_set_int(ctx->priv_data, "surfaces", 1, 0);     // 最少表面数

        // 质量设置
        av_opt_set_int(ctx->priv_data, "cq", 20, 0);   // 恒定质量 (18-28)
        av_opt_set_int(ctx->priv_data, "qmin", 18, 0); // 最小量化参数
        av_opt_set_int(ctx->priv_data, "qmax", 28, 0); // 最大量化参数

        std::cout << "NVENC configured with low-latency settings" << std::endl;
        return true;
    }
    catch (...)
    {
        std::cerr << "Exception configuring NVENC" << std::endl;
        return false;
    }
}

bool HardwareEncoderManager::ConfigureAMF(AVCodecContext *ctx, int width, int height, int fps, int bitrate)
{
    try
    {
        // AMF特定参数
        av_opt_set(ctx->priv_data, "usage", "lowlatency", 0);   // 低延迟用法
        av_opt_set(ctx->priv_data, "profile", "high", 0);       // High Profile
        av_opt_set(ctx->priv_data, "quality", "balanced", 0);   // 平衡质量
        av_opt_set(ctx->priv_data, "rc", "cbr", 0);             // 恒定码率
        av_opt_set_int(ctx->priv_data, "enforce_hrd", 1, 0);    // 强制HRD兼容
        av_opt_set_int(ctx->priv_data, "filler_data", 1, 0);    // 填充数据
        av_opt_set_int(ctx->priv_data, "frame_skipping", 0, 0); // 禁用跳帧

        // 质量设置
        av_opt_set_int(ctx->priv_data, "qp_i", 22, 0); // I帧量化参数
        av_opt_set_int(ctx->priv_data, "qp_p", 24, 0); // P帧量化参数

        std::cout << "AMF configured with low-latency settings" << std::endl;
        return true;
    }
    catch (...)
    {
        std::cerr << "Exception configuring AMF" << std::endl;
        return false;
    }
}

bool HardwareEncoderManager::ConfigureQSV(AVCodecContext *ctx, int width, int height, int fps, int bitrate)
{
    try
    {
        // QSV特定参数
        av_opt_set(ctx->priv_data, "preset", "medium", 0);        // 预设
        av_opt_set(ctx->priv_data, "profile", "high", 0);         // High Profile
        av_opt_set_int(ctx->priv_data, "async_depth", 1, 0);      // 异步深度 (低延迟)
        av_opt_set_int(ctx->priv_data, "look_ahead", 0, 0);       // 禁用前瞻
        av_opt_set_int(ctx->priv_data, "look_ahead_depth", 0, 0); // 前瞻深度
        av_opt_set_int(ctx->priv_data, "low_power", 0, 0);        // 禁用低功耗模式

        // 码率控制
        av_opt_set(ctx->priv_data, "ratecontrol", "cbr", 0); // 恒定码率

        std::cout << "QSV configured with low-latency settings" << std::endl;
        return true;
    }
    catch (...)
    {
        std::cerr << "Exception configuring QSV" << std::endl;
        return false;
    }
}

bool HardwareEncoderManager::ConfigureSoftware(AVCodecContext *ctx, int width, int height, int fps, int bitrate)
{
    try
    {
        // libx264参数 (保持与原有实现兼容)
        av_opt_set(ctx->priv_data, "preset", "medium", 0);
        av_opt_set(ctx->priv_data, "profile", "high", 0);
        av_opt_set(ctx->priv_data, "level", "4.0", 0);
        av_opt_set(ctx->priv_data, "crf", "18", 0);
        av_opt_set(ctx->priv_data, "tune", "zerolatency", 0); // 零延迟调优

        std::cout << "Software encoder configured with high-quality settings" << std::endl;
        return true;
    }
    catch (...)
    {
        std::cerr << "Exception configuring software encoder" << std::endl;
        return false;
    }
}

int HardwareEncoderManager::CalculateOptimalBitrate(int width, int height, int fps, EncoderType encoder_type)
{
    // 基础码率计算 (基于像素数和帧率)
    int pixel_count = width * height;
    double base_bitrate = 0.0;

    // 根据分辨率确定基础码率
    if (pixel_count <= 307200)
    {                          // 640x480
        base_bitrate = 800000; // 800kbps
    }
    else if (pixel_count <= 786432)
    {                           // 1024x768
        base_bitrate = 1500000; // 1.5Mbps
    }
    else if (pixel_count <= 921600)
    {                           // 1280x720
        base_bitrate = 2000000; // 2Mbps
    }
    else if (pixel_count <= 2073600)
    {                           // 1920x1080
        base_bitrate = 4000000; // 4Mbps
    }
    else if (pixel_count <= 8294400)
    {                            // 3840x2160 (4K)
        base_bitrate = 15000000; // 15Mbps
    }
    else
    {
        base_bitrate = 20000000; // 20Mbps for higher resolutions
    }

    // 根据帧率调整
    double fps_factor = fps / 30.0; // 以30fps为基准
    base_bitrate *= fps_factor;

    // 根据编码器类型调整
    switch (encoder_type)
    {
    case ENCODER_NVENC:
        base_bitrate *= 0.9; // NVENC效率较高，可以降低10%码率
        break;
    case ENCODER_AMF:
        base_bitrate *= 0.95; // AMF效率中等，降低5%码率
        break;
    case ENCODER_QSV:
        base_bitrate *= 1.0; // QSV保持基础码率
        break;
    case ENCODER_SOFTWARE:
        base_bitrate *= 0.8; // 软件编码效率最高，可以降低20%码率
        break;
    default:
        break;
    }

    return static_cast<int>(base_bitrate);
}

AVPixelFormat HardwareEncoderManager::GetPreferredPixelFormat(EncoderType type)
{
    switch (type)
    {
    case ENCODER_NVENC:
    case ENCODER_AMF:
    case ENCODER_QSV:
        return AV_PIX_FMT_NV12; // 硬件编码器偏好NV12格式
    case ENCODER_SOFTWARE:
        return AV_PIX_FMT_YUV420P; // 软件编码器使用YUV420P
    default:
        return AV_PIX_FMT_YUV420P;
    }
}

// HardwarePixelConverter 实现
HardwarePixelConverter::HardwarePixelConverter()
    : sws_context_(nullptr), src_width_(0), src_height_(0), dst_format_(AV_PIX_FMT_NONE), initialized_(false)
{
}

HardwarePixelConverter::~HardwarePixelConverter()
{
    Cleanup();
}

bool HardwarePixelConverter::Initialize(int src_width, int src_height, AVPixelFormat dst_format)
{
    Cleanup();

    src_width_ = src_width;
    src_height_ = src_height;
    dst_format_ = dst_format;

    // 创建像素格式转换上下文
    sws_context_ = sws_getContext(
        src_width, src_height, AV_PIX_FMT_BGRA, // 源格式
        src_width, src_height, dst_format,      // 目标格式
        SWS_BILINEAR,                           // 缩放算法
        nullptr, nullptr, nullptr);

    if (!sws_context_)
    {
        std::cerr << "Failed to create pixel format converter" << std::endl;
        return false;
    }

    initialized_ = true;
    std::cout << "Pixel converter initialized: BGRA -> "
              << av_get_pix_fmt_name(dst_format) << std::endl;
    return true;
}

bool HardwarePixelConverter::ConvertBGRAToFrame(const std::vector<uint8_t> &bgra_data, AVFrame *dst_frame)
{
    if (!initialized_ || !dst_frame)
    {
        return false;
    }

    // 设置源数据
    const uint8_t *src_data[1] = {bgra_data.data()};
    int src_linesize[1] = {src_width_ * 4}; // BGRA每像素4字节

    // 执行转换
    int ret = sws_scale(sws_context_,
                        src_data, src_linesize, 0, src_height_,
                        dst_frame->data, dst_frame->linesize);

    return ret == src_height_;
}

void HardwarePixelConverter::Cleanup()
{
    if (sws_context_)
    {
        sws_freeContext(sws_context_);
        sws_context_ = nullptr;
    }

    initialized_ = false;
    temp_buffer_.clear();
}

// HardwareEncoderHelper 实现
namespace HardwareEncoderHelper
{
    bool IsHardwareEncodingSupported()
    {
        // 检查是否有任何硬件编码器可用
        return (avcodec_find_encoder_by_name("h264_nvenc") != nullptr ||
                avcodec_find_encoder_by_name("h264_amf") != nullptr ||
                avcodec_find_encoder_by_name("h264_qsv") != nullptr);
    }

    std::string GetGPUInfo()
    {
        std::stringstream info;

        // 检查NVIDIA
        if (avcodec_find_encoder_by_name("h264_nvenc"))
        {
            info << "NVIDIA GPU detected (NVENC available); ";
        }

        // 检查AMD
        if (avcodec_find_encoder_by_name("h264_amf"))
        {
            info << "AMD GPU detected (AMF available); ";
        }

        // 检查Intel
        if (avcodec_find_encoder_by_name("h264_qsv"))
        {
            info << "Intel GPU detected (QSV available); ";
        }

        std::string result = info.str();
        if (result.empty())
        {
            return "No hardware encoders detected";
        }

        // 移除最后的分号和空格
        if (result.length() > 2)
        {
            result = result.substr(0, result.length() - 2);
        }

        return result;
    }

    std::string GetRecommendedSettings(int width, int height, int fps,
                                       HardwareEncoderManager::EncoderType encoder_type)
    {
        std::stringstream settings;

        settings << "Recommended settings for " << HardwareEncoderManager::GetEncoderName(encoder_type) << ":\n";
        settings << "  Resolution: " << width << "x" << height << "\n";
        settings << "  Frame rate: " << fps << " fps\n";

        switch (encoder_type)
        {
        case HardwareEncoderManager::ENCODER_NVENC:
            settings << "  Preset: p4 (balanced)\n";
            settings << "  Tune: ll (low latency)\n";
            settings << "  Rate control: CBR\n";
            settings << "  Expected CPU usage: 5-10%\n";
            break;

        case HardwareEncoderManager::ENCODER_AMF:
            settings << "  Usage: lowlatency\n";
            settings << "  Quality: balanced\n";
            settings << "  Rate control: CBR\n";
            settings << "  Expected CPU usage: 5-10%\n";
            break;

        case HardwareEncoderManager::ENCODER_QSV:
            settings << "  Preset: medium\n";
            settings << "  Async depth: 1 (low latency)\n";
            settings << "  Rate control: CBR\n";
            settings << "  Expected CPU usage: 10-15%\n";
            break;

        case HardwareEncoderManager::ENCODER_SOFTWARE:
            settings << "  Preset: medium\n";
            settings << "  Profile: high\n";
            settings << "  CRF: 18 (high quality)\n";
            settings << "  Expected CPU usage: 80-100%\n";
            break;

        default:
            settings << "  Unknown encoder type\n";
        }

        return settings.str();
    }
}
