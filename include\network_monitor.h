#pragma once

#include <thread>
#include <atomic>
#include <mutex>
#include <deque>
#include <chrono>
#include <string>

/**
 * @brief 网络状况枚举
 */
enum class NetworkCondition
{
    EXCELLENT,    // 网络状况优秀 (>10Mbps, <50ms RTT, <1% loss)
    GOOD,         // 网络状况良好 (5-10Mbps, 50-100ms RTT, 1-2% loss)
    FAIR,         // 网络状况一般 (2-5Mbps, 100-200ms RTT, 2-5% loss)
    POOR,         // 网络状况较差 (1-2Mbps, 200-500ms RTT, 5-10% loss)
    CRITICAL,     // 网络状况严重 (<1Mbps, >500ms RTT, >10% loss)
    UNKNOWN       // 未知状况
};

/**
 * @brief 网络监控器
 * 
 * 监控网络状况，包括带宽、延迟、丢包率等指标
 */
class NetworkMonitor
{
public:
    /**
     * @brief 网络指标结构
     */
    struct NetworkMetrics
    {
        double bandwidth_mbps;                              // 可用带宽 (Mbps)
        double used_bandwidth_mbps;                         // 已用带宽 (Mbps)
        double rtt_ms;                                      // 往返延迟 (ms)
        double packet_loss_rate;                            // 丢包率 (%)
        double jitter_ms;                                   // 抖动 (ms)
        bool is_stable;                                     // 连接是否稳定
        std::chrono::steady_clock::time_point last_update;  // 最后更新时间
        
        NetworkMetrics()
            : bandwidth_mbps(0.0)
            , used_bandwidth_mbps(0.0)
            , rtt_ms(0.0)
            , packet_loss_rate(0.0)
            , jitter_ms(0.0)
            , is_stable(false)
        {
            last_update = std::chrono::steady_clock::now();
        }
    };

    /**
     * @brief 网络统计信息
     */
    struct NetworkStats
    {
        std::atomic<uint64_t> total_bytes_sent{0};
        std::atomic<uint64_t> total_bytes_received{0};
        std::atomic<uint64_t> total_packets_sent{0};
        std::atomic<uint64_t> total_packets_lost{0};
        std::atomic<uint32_t> connection_drops{0};
        std::chrono::steady_clock::time_point start_time;
        
        void Reset()
        {
            total_bytes_sent = 0;
            total_bytes_received = 0;
            total_packets_sent = 0;
            total_packets_lost = 0;
            connection_drops = 0;
            start_time = std::chrono::steady_clock::now();
        }
        
        double GetAverageBandwidth() const
        {
            auto now = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - start_time);
            return duration.count() > 0 ? 
                (double)total_bytes_sent.load() * 8 / duration.count() / 1000000.0 : 0.0; // Mbps
        }
    };

public:
    /**
     * @brief 构造函数
     * @param target_host 目标主机地址（用于延迟测试）
     * @param monitor_interval 监控间隔（毫秒）
     */
    NetworkMonitor(const std::string& target_host = "*******", 
                  std::chrono::milliseconds monitor_interval = std::chrono::milliseconds(5000))
        : target_host_(target_host)
        , monitor_interval_(monitor_interval)
        , running_(false)
        , history_size_(20)  // 保留最近20个数据点
    {
        stats_.Reset();
    }

    /**
     * @brief 析构函数
     */
    ~NetworkMonitor()
    {
        Stop();
    }

    /**
     * @brief 启动网络监控
     * @return 是否启动成功
     */
    bool Start()
    {
        if (running_) {
            return false;
        }

        running_ = true;
        monitor_thread_ = std::thread(&NetworkMonitor::MonitorLoop, this);
        return true;
    }

    /**
     * @brief 停止网络监控
     */
    void Stop()
    {
        if (!running_) {
            return;
        }

        running_ = false;
        if (monitor_thread_.joinable()) {
            monitor_thread_.join();
        }
    }

    /**
     * @brief 获取当前网络指标
     * @return 网络指标
     */
    NetworkMetrics GetMetrics() const
    {
        std::lock_guard<std::mutex> lock(metrics_mutex_);
        return current_metrics_;
    }

    /**
     * @brief 获取网络统计信息
     * @return 统计信息
     */
    const NetworkStats& GetStats() const
    {
        return stats_;
    }

    /**
     * @brief 获取当前网络状况
     * @return 网络状况
     */
    NetworkCondition GetNetworkCondition() const
    {
        std::lock_guard<std::mutex> lock(metrics_mutex_);
        return AnalyzeCondition(current_metrics_);
    }

    /**
     * @brief 获取网络状况描述
     * @param condition 网络状况
     * @return 描述字符串
     */
    static std::string GetConditionDescription(NetworkCondition condition)
    {
        switch (condition) {
            case NetworkCondition::EXCELLENT: return "Excellent";
            case NetworkCondition::GOOD: return "Good";
            case NetworkCondition::FAIR: return "Fair";
            case NetworkCondition::POOR: return "Poor";
            case NetworkCondition::CRITICAL: return "Critical";
            case NetworkCondition::UNKNOWN: return "Unknown";
            default: return "Invalid";
        }
    }

    /**
     * @brief 更新发送统计
     * @param bytes 发送字节数
     */
    void UpdateSentBytes(uint64_t bytes)
    {
        stats_.total_bytes_sent += bytes;
    }

    /**
     * @brief 更新接收统计
     * @param bytes 接收字节数
     */
    void UpdateReceivedBytes(uint64_t bytes)
    {
        stats_.total_bytes_received += bytes;
    }

    /**
     * @brief 更新包统计
     * @param sent 发送包数
     * @param lost 丢失包数
     */
    void UpdatePacketStats(uint64_t sent, uint64_t lost)
    {
        stats_.total_packets_sent += sent;
        stats_.total_packets_lost += lost;
    }

    /**
     * @brief 记录连接中断
     */
    void RecordConnectionDrop()
    {
        stats_.connection_drops++;
    }

    /**
     * @brief 检查是否正在运行
     * @return 是否正在运行
     */
    bool IsRunning() const
    {
        return running_;
    }

    /**
     * @brief 设置目标主机
     * @param host 目标主机地址
     */
    void SetTargetHost(const std::string& host)
    {
        target_host_ = host;
    }

    /**
     * @brief 设置监控间隔
     * @param interval 监控间隔
     */
    void SetMonitorInterval(std::chrono::milliseconds interval)
    {
        monitor_interval_ = interval;
    }

private:
    /**
     * @brief 监控循环
     */
    void MonitorLoop()
    {
        while (running_) {
            try {
                NetworkMetrics new_metrics;
                
                // 更新各项指标
                UpdateBandwidth(new_metrics);
                UpdateLatency(new_metrics);
                UpdatePacketLoss(new_metrics);
                UpdateJitter(new_metrics);
                UpdateStability(new_metrics);
                
                new_metrics.last_update = std::chrono::steady_clock::now();
                
                // 更新当前指标
                {
                    std::lock_guard<std::mutex> lock(metrics_mutex_);
                    current_metrics_ = new_metrics;
                }
                
                // 更新历史数据
                UpdateHistory(new_metrics);
                
            } catch (...) {
                // 监控过程中的异常不应该中断监控线程
            }
            
            std::this_thread::sleep_for(monitor_interval_);
        }
    }

    /**
     * @brief 更新带宽信息
     */
    void UpdateBandwidth(NetworkMetrics& metrics)
    {
        // 基于发送统计计算当前使用带宽
        static auto last_time = std::chrono::steady_clock::now();
        static uint64_t last_bytes = 0;
        
        auto now = std::chrono::steady_clock::now();
        uint64_t current_bytes = stats_.total_bytes_sent.load();
        
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - last_time);
        if (duration.count() > 0) {
            uint64_t bytes_diff = current_bytes - last_bytes;
            metrics.used_bandwidth_mbps = (double)bytes_diff * 8 / duration.count() / 1000.0; // Mbps
        }
        
        last_time = now;
        last_bytes = current_bytes;
        
        // 估算可用带宽（简化实现，实际可能需要更复杂的测试）
        metrics.bandwidth_mbps = std::max(metrics.used_bandwidth_mbps * 1.5, 10.0); // 假设可用带宽至少是当前使用的1.5倍
    }

    /**
     * @brief 更新延迟信息
     */
    void UpdateLatency(NetworkMetrics& metrics)
    {
        // 简化的ping实现（实际应该使用ICMP或TCP连接测试）
        auto start = std::chrono::high_resolution_clock::now();
        
        // 这里应该实现真正的网络延迟测试
        // 为了简化，使用模拟值
        metrics.rtt_ms = 50.0 + (rand() % 100); // 50-150ms的模拟延迟
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        // metrics.rtt_ms = duration.count() / 1000.0; // 实际实现时使用这行
    }

    /**
     * @brief 更新丢包率
     */
    void UpdatePacketLoss(NetworkMetrics& metrics)
    {
        uint64_t total_sent = stats_.total_packets_sent.load();
        uint64_t total_lost = stats_.total_packets_lost.load();
        
        if (total_sent > 0) {
            metrics.packet_loss_rate = (double)total_lost / total_sent * 100.0;
        } else {
            metrics.packet_loss_rate = 0.0;
        }
    }

    /**
     * @brief 更新抖动信息
     */
    void UpdateJitter(NetworkMetrics& metrics)
    {
        // 基于RTT历史计算抖动
        if (rtt_history_.size() >= 2) {
            double sum_diff = 0.0;
            for (size_t i = 1; i < rtt_history_.size(); ++i) {
                sum_diff += std::abs(rtt_history_[i] - rtt_history_[i-1]);
            }
            metrics.jitter_ms = sum_diff / (rtt_history_.size() - 1);
        } else {
            metrics.jitter_ms = 0.0;
        }
    }

    /**
     * @brief 更新连接稳定性
     */
    void UpdateStability(NetworkMetrics& metrics)
    {
        // 基于连接中断次数和指标变化判断稳定性
        uint32_t drops = stats_.connection_drops.load();
        bool low_jitter = metrics.jitter_ms < 20.0;
        bool low_loss = metrics.packet_loss_rate < 2.0;
        bool reasonable_rtt = metrics.rtt_ms < 200.0;
        
        metrics.is_stable = (drops < 3) && low_jitter && low_loss && reasonable_rtt;
    }

    /**
     * @brief 更新历史数据
     */
    void UpdateHistory(const NetworkMetrics& metrics)
    {
        std::lock_guard<std::mutex> lock(history_mutex_);
        
        // 更新带宽历史
        bandwidth_history_.push_back(metrics.bandwidth_mbps);
        if (bandwidth_history_.size() > history_size_) {
            bandwidth_history_.pop_front();
        }
        
        // 更新RTT历史
        rtt_history_.push_back(metrics.rtt_ms);
        if (rtt_history_.size() > history_size_) {
            rtt_history_.pop_front();
        }
        
        // 更新丢包率历史
        loss_history_.push_back(metrics.packet_loss_rate);
        if (loss_history_.size() > history_size_) {
            loss_history_.pop_front();
        }
    }

    /**
     * @brief 分析网络状况
     */
    NetworkCondition AnalyzeCondition(const NetworkMetrics& metrics) const
    {
        // 基于多个指标综合判断网络状况
        if (metrics.bandwidth_mbps >= 10.0 && metrics.rtt_ms <= 50.0 && metrics.packet_loss_rate <= 1.0) {
            return NetworkCondition::EXCELLENT;
        } else if (metrics.bandwidth_mbps >= 5.0 && metrics.rtt_ms <= 100.0 && metrics.packet_loss_rate <= 2.0) {
            return NetworkCondition::GOOD;
        } else if (metrics.bandwidth_mbps >= 2.0 && metrics.rtt_ms <= 200.0 && metrics.packet_loss_rate <= 5.0) {
            return NetworkCondition::FAIR;
        } else if (metrics.bandwidth_mbps >= 1.0 && metrics.rtt_ms <= 500.0 && metrics.packet_loss_rate <= 10.0) {
            return NetworkCondition::POOR;
        } else {
            return NetworkCondition::CRITICAL;
        }
    }

private:
    // 配置参数
    std::string target_host_;
    std::chrono::milliseconds monitor_interval_;
    size_t history_size_;

    // 线程控制
    std::thread monitor_thread_;
    std::atomic<bool> running_;

    // 当前指标
    mutable std::mutex metrics_mutex_;
    NetworkMetrics current_metrics_;

    // 统计信息
    NetworkStats stats_;

    // 历史数据
    mutable std::mutex history_mutex_;
    std::deque<double> bandwidth_history_;
    std::deque<double> rtt_history_;
    std::deque<double> loss_history_;

    // 禁用拷贝构造和赋值
    NetworkMonitor(const NetworkMonitor&) = delete;
    NetworkMonitor& operator=(const NetworkMonitor&) = delete;
};
