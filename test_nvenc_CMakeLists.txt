cmake_minimum_required(VERSION 3.16)
project(test_nvenc)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置vcpkg路径
set(VCPKG_ROOT "C:/dev/vcpkg/installed/x64-windows")
set(CMAKE_PREFIX_PATH "${VCPKG_ROOT};${CMAKE_PREFIX_PATH}")

# FFmpeg库
set(FFMPEG_INCLUDE_DIRS "${VCPKG_ROOT}/include")
set(FFMPEG_LIBRARIES 
    "${VCPKG_ROOT}/lib/avcodec.lib"
    "${VCPKG_ROOT}/lib/avutil.lib"
)

# 包含目录
include_directories(${FFMPEG_INCLUDE_DIRS})

# 创建可执行文件
add_executable(test_nvenc test_nvenc.cpp)

# 链接库
target_link_libraries(test_nvenc ${FFMPEG_LIBRARIES})
