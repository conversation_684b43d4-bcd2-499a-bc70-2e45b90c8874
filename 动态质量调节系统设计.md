# 动态质量调节系统设计

## 概述

动态质量调节系统是一个智能的自适应机制，能够根据网络状况、系统性能和用户需求实时调整编码质量、码率和帧率，确保在各种环境下都能提供最佳的推流体验。

## 系统架构

### 1. 核心组件

```
网络监控器 ←→ 质量控制器 ←→ 性能监控器
     ↓              ↓              ↓
  网络指标      质量决策      系统指标
     ↓              ↓              ↓
     └──────→ 编码参数调整 ←──────┘
                    ↓
              编码器/捕获器
```

### 2. 监控指标

#### 2.1 网络指标
- **带宽利用率**: 当前使用带宽 / 可用带宽
- **网络延迟**: RTT (Round Trip Time)
- **丢包率**: 网络包丢失百分比
- **抖动**: 延迟变化程度
- **连接稳定性**: 连接中断频率

#### 2.2 系统性能指标
- **CPU使用率**: 编码和捕获的CPU占用
- **GPU使用率**: 硬件编码器使用情况
- **内存使用率**: 系统内存占用
- **队列状态**: 帧队列和包队列的填充情况
- **编码延迟**: 从捕获到编码完成的时间

#### 2.3 质量指标
- **实际帧率**: 当前达到的帧率
- **实际码率**: 当前编码输出码率
- **关键帧间隔**: I帧间隔时间
- **编码质量**: 量化参数等质量指标

### 3. 调节策略

#### 3.1 网络自适应策略
```cpp
enum NetworkCondition {
    EXCELLENT,    // 网络状况优秀
    GOOD,         // 网络状况良好
    FAIR,         // 网络状况一般
    POOR,         // 网络状况较差
    CRITICAL      // 网络状况严重
};

struct NetworkAdaptiveProfile {
    int max_bitrate;      // 最大码率
    int target_fps;       // 目标帧率
    int gop_size;         // GOP大小
    std::string preset;   // 编码预设
    bool enable_bframes;  // 是否启用B帧
};
```

#### 3.2 性能自适应策略
```cpp
enum PerformanceLevel {
    HIGH_PERFORMANCE,     // 高性能模式
    BALANCED,            // 平衡模式
    POWER_SAVING,        // 节能模式
    EMERGENCY            // 紧急模式
};

struct PerformanceProfile {
    int capture_fps;          // 捕获帧率
    int encoding_threads;     // 编码线程数
    std::string encoder_type; // 编码器类型
    int queue_size;          // 队列大小
    bool enable_gpu_accel;   // 是否启用GPU加速
};
```

## 实现设计

### 1. 网络监控器

```cpp
class NetworkMonitor {
public:
    struct NetworkMetrics {
        double bandwidth_mbps;        // 可用带宽 (Mbps)
        double used_bandwidth_mbps;   // 已用带宽 (Mbps)
        double rtt_ms;               // 往返延迟 (ms)
        double packet_loss_rate;     // 丢包率 (%)
        double jitter_ms;            // 抖动 (ms)
        bool is_stable;              // 连接是否稳定
        std::chrono::steady_clock::time_point last_update;
    };

private:
    std::thread monitor_thread_;
    std::atomic<bool> running_;
    NetworkMetrics current_metrics_;
    std::mutex metrics_mutex_;
    
    // 历史数据用于趋势分析
    std::deque<double> bandwidth_history_;
    std::deque<double> rtt_history_;
    std::deque<double> loss_history_;

public:
    bool Start();
    void Stop();
    NetworkMetrics GetMetrics() const;
    NetworkCondition GetNetworkCondition() const;
    
private:
    void MonitorLoop();
    void UpdateBandwidth();
    void UpdateLatency();
    void UpdatePacketLoss();
    NetworkCondition AnalyzeCondition(const NetworkMetrics& metrics) const;
};
```

### 2. 性能监控器

```cpp
class PerformanceMonitor {
public:
    struct SystemMetrics {
        double cpu_usage_percent;     // CPU使用率
        double gpu_usage_percent;     // GPU使用率
        double memory_usage_percent;  // 内存使用率
        double encoding_fps;          // 编码帧率
        double capture_fps;           // 捕获帧率
        size_t frame_queue_size;      // 帧队列大小
        size_t packet_queue_size;     // 包队列大小
        double avg_encoding_time_ms;  // 平均编码时间
        std::chrono::steady_clock::time_point last_update;
    };

private:
    std::thread monitor_thread_;
    std::atomic<bool> running_;
    SystemMetrics current_metrics_;
    std::mutex metrics_mutex_;

public:
    bool Start();
    void Stop();
    SystemMetrics GetMetrics() const;
    PerformanceLevel GetPerformanceLevel() const;
    
private:
    void MonitorLoop();
    void UpdateCPUUsage();
    void UpdateGPUUsage();
    void UpdateMemoryUsage();
    PerformanceLevel AnalyzePerformance(const SystemMetrics& metrics) const;
};
```

### 3. 质量控制器

```cpp
class QualityController {
public:
    struct QualitySettings {
        int target_bitrate;           // 目标码率
        int target_fps;               // 目标帧率
        int capture_width;            // 捕获宽度
        int capture_height;           // 捕获高度
        std::string encoder_preset;   // 编码预设
        int gop_size;                // GOP大小
        bool adaptive_bitrate;        // 是否启用自适应码率
        bool adaptive_fps;            // 是否启用自适应帧率
        bool adaptive_resolution;     // 是否启用自适应分辨率
    };

private:
    std::unique_ptr<NetworkMonitor> network_monitor_;
    std::unique_ptr<PerformanceMonitor> performance_monitor_;
    
    QualitySettings current_settings_;
    QualitySettings target_settings_;
    std::mutex settings_mutex_;
    
    // 调节策略
    std::map<NetworkCondition, NetworkAdaptiveProfile> network_profiles_;
    std::map<PerformanceLevel, PerformanceProfile> performance_profiles_;
    
    // 调节控制
    std::thread adjustment_thread_;
    std::atomic<bool> running_;
    std::chrono::milliseconds adjustment_interval_;

public:
    bool Initialize(const QualitySettings& initial_settings);
    bool Start();
    void Stop();
    
    QualitySettings GetCurrentSettings() const;
    void SetTargetSettings(const QualitySettings& settings);
    
    // 手动调节接口
    void AdjustBitrate(int bitrate);
    void AdjustFrameRate(int fps);
    void AdjustResolution(int width, int height);
    void SetEncoderPreset(const std::string& preset);
    
    // 自适应控制
    void EnableAdaptiveBitrate(bool enable);
    void EnableAdaptiveFrameRate(bool enable);
    void EnableAdaptiveResolution(bool enable);

private:
    void AdjustmentLoop();
    void AnalyzeAndAdjust();
    
    // 调节算法
    QualitySettings CalculateOptimalSettings(
        const NetworkMonitor::NetworkMetrics& network_metrics,
        const PerformanceMonitor::SystemMetrics& system_metrics) const;
    
    void ApplyNetworkAdaptation(QualitySettings& settings, NetworkCondition condition) const;
    void ApplyPerformanceAdaptation(QualitySettings& settings, PerformanceLevel level) const;
    
    // 平滑调节
    void SmoothTransition(const QualitySettings& from, const QualitySettings& to);
    bool ShouldAdjust(const QualitySettings& current, const QualitySettings& target) const;
};
```

### 4. 自适应算法

#### 4.1 网络自适应算法
```cpp
QualitySettings QualityController::AdaptToNetwork(
    const NetworkMonitor::NetworkMetrics& metrics) const 
{
    QualitySettings settings = current_settings_;
    
    // 基于可用带宽调整码率
    double available_bandwidth = metrics.bandwidth_mbps * 0.8; // 保留20%余量
    int max_bitrate = static_cast<int>(available_bandwidth * 1000); // 转换为kbps
    
    if (settings.target_bitrate > max_bitrate) {
        settings.target_bitrate = max_bitrate;
    }
    
    // 基于延迟调整GOP大小
    if (metrics.rtt_ms > 200) {
        settings.gop_size = std::min(settings.gop_size, settings.target_fps); // 1秒GOP
    } else if (metrics.rtt_ms < 50) {
        settings.gop_size = settings.target_fps * 2; // 2秒GOP
    }
    
    // 基于丢包率调整帧率
    if (metrics.packet_loss_rate > 5.0) {
        settings.target_fps = std::max(15, settings.target_fps - 5);
    } else if (metrics.packet_loss_rate < 1.0) {
        settings.target_fps = std::min(60, settings.target_fps + 5);
    }
    
    return settings;
}
```

#### 4.2 性能自适应算法
```cpp
QualitySettings QualityController::AdaptToPerformance(
    const PerformanceMonitor::SystemMetrics& metrics) const 
{
    QualitySettings settings = current_settings_;
    
    // 基于CPU使用率调整编码预设
    if (metrics.cpu_usage_percent > 90) {
        settings.encoder_preset = "ultrafast";
        settings.target_fps = std::max(15, settings.target_fps - 10);
    } else if (metrics.cpu_usage_percent > 70) {
        settings.encoder_preset = "veryfast";
        settings.target_fps = std::max(20, settings.target_fps - 5);
    } else if (metrics.cpu_usage_percent < 50) {
        settings.encoder_preset = "medium";
    }
    
    // 基于队列状态调整参数
    if (metrics.frame_queue_size > 8) {
        // 队列积压，降低捕获帧率
        settings.target_fps = std::max(15, settings.target_fps - 5);
    }
    
    if (metrics.packet_queue_size > 15) {
        // 包队列积压，降低码率
        settings.target_bitrate = static_cast<int>(settings.target_bitrate * 0.8);
    }
    
    return settings;
}
```

### 5. 集成接口

```cpp
class AdaptiveStreamManager {
private:
    std::unique_ptr<QualityController> quality_controller_;
    std::unique_ptr<CaptureThreadManager> capture_manager_;
    std::unique_ptr<EncodingThreadManager> encoding_manager_;
    std::unique_ptr<StreamingThreadManager> streaming_manager_;

public:
    bool Initialize(const StreamConfig& config);
    bool Start();
    void Stop();
    
    // 质量控制接口
    void EnableAdaptiveQuality(bool enable);
    void SetQualityProfile(const std::string& profile); // "high", "medium", "low", "auto"
    
    // 状态查询
    QualityController::QualitySettings GetCurrentQuality() const;
    NetworkMonitor::NetworkMetrics GetNetworkStatus() const;
    PerformanceMonitor::SystemMetrics GetSystemStatus() const;
    
private:
    void OnQualityChanged(const QualityController::QualitySettings& new_settings);
    void ApplyQualitySettings(const QualityController::QualitySettings& settings);
};
```

## 预设配置

### 1. 质量预设
```cpp
// 高质量预设
QualitySettings HIGH_QUALITY = {
    .target_bitrate = 6000,      // 6Mbps
    .target_fps = 60,            // 60fps
    .capture_width = 1920,       // 1080p
    .capture_height = 1080,
    .encoder_preset = "medium",
    .gop_size = 120,            // 2秒GOP
    .adaptive_bitrate = true,
    .adaptive_fps = false,
    .adaptive_resolution = false
};

// 平衡预设
QualitySettings BALANCED = {
    .target_bitrate = 3000,      // 3Mbps
    .target_fps = 30,            // 30fps
    .capture_width = 1920,       // 1080p
    .capture_height = 1080,
    .encoder_preset = "fast",
    .gop_size = 60,             // 2秒GOP
    .adaptive_bitrate = true,
    .adaptive_fps = true,
    .adaptive_resolution = false
};

// 低延迟预设
QualitySettings LOW_LATENCY = {
    .target_bitrate = 2000,      // 2Mbps
    .target_fps = 30,            // 30fps
    .capture_width = 1280,       // 720p
    .capture_height = 720,
    .encoder_preset = "veryfast",
    .gop_size = 30,             // 1秒GOP
    .adaptive_bitrate = true,
    .adaptive_fps = true,
    .adaptive_resolution = true
};
```

### 2. 网络条件预设
```cpp
std::map<NetworkCondition, NetworkAdaptiveProfile> NETWORK_PROFILES = {
    {EXCELLENT, {8000, 60, 120, "medium", true}},
    {GOOD,      {4000, 30, 60,  "fast",   true}},
    {FAIR,      {2000, 25, 50,  "veryfast", false}},
    {POOR,      {1000, 20, 40,  "ultrafast", false}},
    {CRITICAL,  {500,  15, 30,  "ultrafast", false}}
};
```

## 实施计划

### 阶段1: 监控系统 (3天)
- [ ] 实现NetworkMonitor类
- [ ] 实现PerformanceMonitor类
- [ ] 基础指标收集和分析

### 阶段2: 质量控制器 (3天)
- [ ] 实现QualityController类
- [ ] 自适应算法实现
- [ ] 预设配置系统

### 阶段3: 集成和测试 (2天)
- [ ] 集成到现有系统
- [ ] 全面测试和调优
- [ ] 性能验证

这个动态质量调节系统将显著提升推流的适应性和稳定性，确保在各种网络和系统条件下都能提供最佳的用户体验。
