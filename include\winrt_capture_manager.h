#pragma once

#include <memory>
#include <vector>
#include <windows.h>
#include <d3d11.h>
#include <dxgi1_2.h>
#include <winrt/base.h>
#include <winrt/Windows.Foundation.h>
#include <winrt/Windows.Graphics.Capture.h>
#include <winrt/Windows.Graphics.DirectX.h>
#include <winrt/Windows.Graphics.DirectX.Direct3D11.h>
#include <windows.graphics.directx.direct3d11.interop.h>
#include <windows.graphics.capture.interop.h>

// 前向声明
class D3D11TextureProcessor;
class FrameBufferPool;

/**
 * @brief 真正的WinRT屏幕捕获管理器
 * 
 * 使用Windows.Graphics.Capture API实现高性能屏幕捕获
 * 支持硬件加速和现代Windows显示技术
 */
class WinRTCaptureManager
{
public:
    WinRTCaptureManager();
    ~WinRTCaptureManager();

    /**
     * @brief 初始化WinRT捕获
     * @param target_window 目标窗口句柄，nullptr表示捕获整个屏幕
     * @param monitor_handle 显示器句柄，用于多显示器支持
     * @return 初始化是否成功
     */
    bool Initialize(HWND target_window = nullptr, HMONITOR monitor_handle = nullptr);

    /**
     * @brief 捕获一帧数据
     * @param frame_data 输出的帧数据 (BGRA格式)
     * @param width 输出帧宽度
     * @param height 输出帧高度
     * @return 捕获是否成功
     */
    bool CaptureFrame(std::vector<uint8_t>& frame_data, int& width, int& height);

    /**
     * @brief 检查WinRT捕获是否可用
     * @return 是否支持WinRT捕获
     */
    static bool IsWinRTCaptureSupported();

    /**
     * @brief 获取捕获尺寸
     */
    int GetWidth() const { return capture_width_; }
    int GetHeight() const { return capture_height_; }

    /**
     * @brief 清理资源
     */
    void Cleanup();

private:
    // WinRT捕获组件
    winrt::Windows::Graphics::Capture::GraphicsCaptureSession capture_session_{ nullptr };
    winrt::Windows::Graphics::Capture::Direct3D11CaptureFramePool frame_pool_{ nullptr };
    winrt::Windows::Graphics::DirectX::Direct3D11::IDirect3DDevice winrt_device_{ nullptr };
    winrt::Windows::Graphics::Capture::GraphicsCaptureItem capture_item_{ nullptr };

    // D3D11设备和上下文
    winrt::com_ptr<ID3D11Device> d3d11_device_;
    winrt::com_ptr<ID3D11DeviceContext> d3d11_context_;

    // 纹理处理器和缓冲池
    std::unique_ptr<D3D11TextureProcessor> texture_processor_;
    std::unique_ptr<FrameBufferPool> buffer_pool_;

    // 捕获参数
    int capture_width_;
    int capture_height_;
    bool initialized_;

    // 私有方法
    bool CreateD3DDevice();
    bool CreateWinRTDevice();
    bool CreateCaptureItem(HWND target_window, HMONITOR monitor_handle);
    bool CreateFramePool();
    bool StartCapture();

    // 事件处理
    void OnFrameArrived(winrt::Windows::Graphics::Capture::Direct3D11CaptureFramePool const& sender,
                       winrt::Windows::Foundation::IInspectable const& args);
};

/**
 * @brief D3D11纹理处理器
 * 
 * 负责将D3D11纹理转换为CPU可访问的内存数据
 */
class D3D11TextureProcessor
{
public:
    D3D11TextureProcessor();
    ~D3D11TextureProcessor();

    /**
     * @brief 初始化处理器
     * @param device D3D11设备
     * @param context D3D11设备上下文
     * @return 初始化是否成功
     */
    bool Initialize(ID3D11Device* device, ID3D11DeviceContext* context);

    /**
     * @brief 将纹理转换为内存数据
     * @param source_texture 源纹理
     * @param output_data 输出数据缓冲区
     * @param width 纹理宽度
     * @param height 纹理高度
     * @return 转换是否成功
     */
    bool ConvertTextureToMemory(ID3D11Texture2D* source_texture, 
                               std::vector<uint8_t>& output_data,
                               int width, int height);

    /**
     * @brief 清理资源
     */
    void Cleanup();

private:
    ID3D11Device* d3d11_device_;
    ID3D11DeviceContext* d3d11_context_;
    winrt::com_ptr<ID3D11Texture2D> staging_texture_;

    int current_width_;
    int current_height_;
    bool initialized_;

    bool CreateStagingTexture(int width, int height);
    bool UpdateStagingTexture(int width, int height);
};

/**
 * @brief 帧缓冲池
 * 
 * 管理帧数据缓冲区，减少内存分配开销
 */
class FrameBufferPool
{
public:
    FrameBufferPool(size_t pool_size = 4);
    ~FrameBufferPool();

    /**
     * @brief 获取缓冲区
     * @param size 所需缓冲区大小
     * @return 缓冲区
     */
    std::vector<uint8_t> GetBuffer(size_t size);

    /**
     * @brief 归还缓冲区
     * @param buffer 要归还的缓冲区
     */
    void ReturnBuffer(std::vector<uint8_t>&& buffer);

    /**
     * @brief 清理池中的缓冲区
     */
    void Clear();

private:
    std::queue<std::vector<uint8_t>> available_buffers_;
    std::mutex buffer_mutex_;
    size_t max_pool_size_;
    size_t current_pool_size_;
};

/**
 * @brief WinRT捕获辅助函数
 */
namespace WinRTCaptureHelper
{
    /**
     * @brief 检查Windows版本是否支持WinRT捕获
     * @return 是否支持
     */
    bool CheckWindowsVersion();

    /**
     * @brief 获取主显示器句柄
     * @return 主显示器句柄
     */
    HMONITOR GetPrimaryMonitor();

    /**
     * @brief 根据坐标获取显示器句柄
     * @param x X坐标
     * @param y Y坐标
     * @return 显示器句柄
     */
    HMONITOR GetMonitorFromPoint(int x, int y);

    /**
     * @brief 获取显示器信息
     * @param monitor 显示器句柄
     * @param width 输出宽度
     * @param height 输出高度
     * @return 获取是否成功
     */
    bool GetMonitorSize(HMONITOR monitor, int& width, int& height);
}
