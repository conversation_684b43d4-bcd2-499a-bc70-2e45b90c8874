#pragma once

#include "thread_safe_queue.h"
#include "frame_data.h"
#include "screen_capture_obs_dynamic.h"
#include <thread>
#include <atomic>
#include <chrono>
#include <memory>

/**
 * @brief 捕获线程管理器
 * 
 * 负责在独立线程中进行屏幕捕获，并将捕获的帧放入队列
 */
class CaptureThreadManager
{
public:
    /**
     * @brief 捕获统计信息
     */
    struct CaptureStats
    {
        std::atomic<uint64_t> total_frames{0};
        std::atomic<uint64_t> successful_frames{0};
        std::atomic<uint64_t> failed_frames{0};
        std::atomic<uint64_t> dropped_frames{0};
        std::atomic<double> avg_capture_time_ms{0.0};
        std::atomic<double> max_capture_time_ms{0.0};
        std::atomic<double> current_fps{0.0};
        std::chrono::steady_clock::time_point start_time;
        
        void Reset()
        {
            total_frames = 0;
            successful_frames = 0;
            failed_frames = 0;
            dropped_frames = 0;
            avg_capture_time_ms = 0.0;
            max_capture_time_ms = 0.0;
            current_fps = 0.0;
            start_time = std::chrono::steady_clock::now();
        }
        
        double GetSuccessRate() const
        {
            uint64_t total = total_frames.load();
            return total > 0 ? (double)successful_frames.load() / total * 100.0 : 0.0;
        }
        
        double GetActualFPS() const
        {
            auto now = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - start_time);
            return duration.count() > 0 ? (double)successful_frames.load() / duration.count() : 0.0;
        }
    };

public:
    /**
     * @brief 构造函数
     * @param frame_queue 帧队列引用
     * @param queue_max_size 队列最大大小
     */
    CaptureThreadManager(ThreadSafeQueue<std::unique_ptr<CaptureFrame>>& frame_queue, 
                        size_t queue_max_size = 10)
        : frame_queue_(frame_queue)
        , running_(false)
        , paused_(false)
        , target_fps_(30)
        , frame_counter_(0)
        , capture_x_(0)
        , capture_y_(0)
        , capture_width_(1920)
        , capture_height_(1080)
    {
        frame_queue_.SetMaxSize(queue_max_size);
        buffer_pool_ = std::make_unique<FrameBufferPool>(queue_max_size, queue_max_size * 2);
        capture_device_ = std::make_unique<ScreenCaptureOBSDynamic>();
        
        // 计算目标帧间隔
        UpdateFrameInterval();
    }

    /**
     * @brief 析构函数
     */
    ~CaptureThreadManager()
    {
        Stop();
    }

    /**
     * @brief 启动捕获线程
     * @param fps 目标帧率
     * @param x 捕获区域X坐标
     * @param y 捕获区域Y坐标
     * @param width 捕获区域宽度
     * @param height 捕获区域高度
     * @return 是否启动成功
     */
    bool Start(int fps, int x = 0, int y = 0, int width = 1920, int height = 1080)
    {
        if (running_) {
            return false;
        }

        target_fps_ = fps;
        capture_x_ = x;
        capture_y_ = y;
        capture_width_ = width;
        capture_height_ = height;
        
        UpdateFrameInterval();

        // 初始化捕获设备
        if (!capture_device_->Initialize(x, y, width, height)) {
            return false;
        }

        // 重置统计信息
        stats_.Reset();
        frame_counter_ = 0;
        
        // 启动捕获线程
        running_ = true;
        paused_ = false;
        capture_thread_ = std::thread(&CaptureThreadManager::CaptureLoop, this);

        return true;
    }

    /**
     * @brief 停止捕获线程
     */
    void Stop()
    {
        if (!running_) {
            return;
        }

        running_ = false;
        
        if (capture_thread_.joinable()) {
            capture_thread_.join();
        }

        if (capture_device_) {
            capture_device_->Cleanup();
        }
    }

    /**
     * @brief 暂停捕获
     */
    void Pause()
    {
        paused_ = true;
    }

    /**
     * @brief 恢复捕获
     */
    void Resume()
    {
        paused_ = false;
    }

    /**
     * @brief 检查是否正在运行
     * @return 是否正在运行
     */
    bool IsRunning() const
    {
        return running_;
    }

    /**
     * @brief 检查是否已暂停
     * @return 是否已暂停
     */
    bool IsPaused() const
    {
        return paused_;
    }

    /**
     * @brief 获取捕获统计信息
     * @return 统计信息
     */
    const CaptureStats& GetStats() const
    {
        return stats_;
    }

    /**
     * @brief 设置目标帧率
     * @param fps 目标帧率
     */
    void SetTargetFPS(int fps)
    {
        target_fps_ = fps;
        UpdateFrameInterval();
    }

    /**
     * @brief 获取目标帧率
     * @return 目标帧率
     */
    int GetTargetFPS() const
    {
        return target_fps_;
    }

    /**
     * @brief 获取队列当前大小
     * @return 队列大小
     */
    size_t GetQueueSize() const
    {
        return frame_queue_.Size();
    }

    /**
     * @brief 清空帧队列
     */
    void ClearQueue()
    {
        frame_queue_.Clear();
    }

private:
    /**
     * @brief 捕获循环（在独立线程中运行）
     */
    void CaptureLoop()
    {
        last_capture_time_ = std::chrono::steady_clock::now();
        
        while (running_) {
            if (paused_) {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
                continue;
            }

            auto capture_start = std::chrono::steady_clock::now();
            
            // 执行帧捕获
            bool success = CaptureFrame();
            
            auto capture_end = std::chrono::steady_clock::now();
            auto capture_duration = std::chrono::duration_cast<std::chrono::microseconds>(
                capture_end - capture_start);

            // 更新统计信息
            UpdateStats(capture_duration.count() / 1000.0, success);

            // 等待下一帧时间
            WaitForNextFrame();
        }
    }

    /**
     * @brief 执行单帧捕获
     * @return 是否成功
     */
    bool CaptureFrame()
    {
        try {
            // 从缓冲池获取帧
            auto frame = buffer_pool_->GetFrame(capture_width_, capture_height_, ++frame_counter_);
            
            if (!frame) {
                stats_.failed_frames++;
                return false;
            }

            // 执行屏幕捕获
            int actual_width, actual_height;
            if (!capture_device_->CaptureFrame(frame->data, actual_width, actual_height)) {
                stats_.failed_frames++;
                buffer_pool_->ReturnFrame(std::move(frame));
                return false;
            }

            // 更新帧信息
            frame->width = actual_width;
            frame->height = actual_height;
            frame->data_size = frame->data.size();
            frame->timestamp = std::chrono::steady_clock::now();

            // 尝试将帧放入队列
            if (!frame_queue_.TryPush(std::move(frame))) {
                // 队列已满，丢弃帧
                stats_.dropped_frames++;
                return false;
            }

            stats_.successful_frames++;
            return true;

        } catch (...) {
            stats_.failed_frames++;
            return false;
        }
    }

    /**
     * @brief 等待下一帧时间
     */
    void WaitForNextFrame()
    {
        auto now = std::chrono::steady_clock::now();
        auto elapsed = now - last_capture_time_;
        
        if (elapsed < target_frame_interval_) {
            auto sleep_time = target_frame_interval_ - elapsed;
            std::this_thread::sleep_for(sleep_time);
        }
        
        last_capture_time_ = std::chrono::steady_clock::now();
    }

    /**
     * @brief 更新帧间隔
     */
    void UpdateFrameInterval()
    {
        target_frame_interval_ = std::chrono::microseconds(1000000 / target_fps_);
    }

    /**
     * @brief 更新统计信息
     * @param capture_time_ms 捕获时间（毫秒）
     * @param success 是否成功
     */
    void UpdateStats(double capture_time_ms, bool success)
    {
        stats_.total_frames++;
        
        if (success) {
            // 更新平均捕获时间
            double current_avg = stats_.avg_capture_time_ms.load();
            double new_avg = (current_avg * (stats_.successful_frames.load() - 1) + capture_time_ms) / 
                           stats_.successful_frames.load();
            stats_.avg_capture_time_ms = new_avg;
            
            // 更新最大捕获时间
            double current_max = stats_.max_capture_time_ms.load();
            if (capture_time_ms > current_max) {
                stats_.max_capture_time_ms = capture_time_ms;
            }
        }
        
        // 每100帧更新一次FPS
        if (stats_.total_frames % 100 == 0) {
            stats_.current_fps = stats_.GetActualFPS();
        }
    }

private:
    // 核心组件
    std::unique_ptr<ScreenCaptureOBSDynamic> capture_device_;
    std::unique_ptr<FrameBufferPool> buffer_pool_;
    ThreadSafeQueue<std::unique_ptr<CaptureFrame>>& frame_queue_;

    // 线程控制
    std::thread capture_thread_;
    std::atomic<bool> running_;
    std::atomic<bool> paused_;

    // 捕获参数
    std::atomic<int> target_fps_;
    int capture_x_, capture_y_;
    int capture_width_, capture_height_;
    
    // 时序控制
    std::chrono::microseconds target_frame_interval_;
    std::chrono::steady_clock::time_point last_capture_time_;
    
    // 统计信息
    CaptureStats stats_;
    std::atomic<uint64_t> frame_counter_;

    // 禁用拷贝构造和赋值
    CaptureThreadManager(const CaptureThreadManager&) = delete;
    CaptureThreadManager& operator=(const CaptureThreadManager&) = delete;
};
