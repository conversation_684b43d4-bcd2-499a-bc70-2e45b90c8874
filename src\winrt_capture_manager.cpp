#include "winrt_capture_manager.h"
#include <iostream>
#include <queue>
#include <mutex>
#include <VersionHelpers.h>

using namespace winrt;
using namespace Windows::Foundation;
using namespace Windows::Graphics::Capture;
using namespace Windows::Graphics::DirectX;
using namespace Windows::Graphics::DirectX::Direct3D11;

// WinRTCaptureManager 实现
WinRTCaptureManager::WinRTCaptureManager()
    : capture_width_(0), capture_height_(0), initialized_(false)
{
    texture_processor_ = std::make_unique<D3D11TextureProcessor>();
    buffer_pool_ = std::make_unique<FrameBufferPool>();
}

WinRTCaptureManager::~WinRTCaptureManager()
{
    Cleanup();
}

bool WinRTCaptureManager::IsWinRTCaptureSupported()
{
    // 检查Windows版本 (需要Windows 10 1903+)
    if (!WinRTCaptureHelper::CheckWindowsVersion())
    {
        return false;
    }

    // 检查WinRT API可用性
    try
    {
        auto supported = GraphicsCaptureSession::IsSupported();
        return supported;
    }
    catch (...)
    {
        return false;
    }
}

bool WinRTCaptureManager::Initialize(HWND target_window, HMONITOR monitor_handle)
{
    try
    {
        std::cout << "Initializing WinRT capture manager..." << std::endl;

        // 1. 创建D3D11设备
        if (!CreateD3DDevice())
        {
            std::cerr << "Failed to create D3D11 device" << std::endl;
            return false;
        }

        // 2. 创建WinRT设备
        if (!CreateWinRTDevice())
        {
            std::cerr << "Failed to create WinRT device" << std::endl;
            return false;
        }

        // 3. 创建捕获项
        if (!CreateCaptureItem(target_window, monitor_handle))
        {
            std::cerr << "Failed to create capture item" << std::endl;
            return false;
        }

        // 4. 创建帧池
        if (!CreateFramePool())
        {
            std::cerr << "Failed to create frame pool" << std::endl;
            return false;
        }

        // 5. 初始化纹理处理器
        if (!texture_processor_->Initialize(d3d11_device_.get(), d3d11_context_.get()))
        {
            std::cerr << "Failed to initialize texture processor" << std::endl;
            return false;
        }

        // 6. 开始捕获
        if (!StartCapture())
        {
            std::cerr << "Failed to start capture" << std::endl;
            return false;
        }

        initialized_ = true;
        std::cout << "WinRT capture initialized successfully!" << std::endl;
        std::cout << "Capture size: " << capture_width_ << "x" << capture_height_ << std::endl;

        return true;
    }
    catch (hresult_error const &ex)
    {
        std::wcerr << L"WinRT error: " << ex.message().c_str() << std::endl;
        return false;
    }
    catch (std::exception const &ex)
    {
        std::cerr << "Exception: " << ex.what() << std::endl;
        return false;
    }
}

bool WinRTCaptureManager::CreateD3DDevice()
{
    // 创建D3D11设备
    D3D_FEATURE_LEVEL feature_levels[] = {
        D3D_FEATURE_LEVEL_11_1,
        D3D_FEATURE_LEVEL_11_0,
        D3D_FEATURE_LEVEL_10_1,
        D3D_FEATURE_LEVEL_10_0};

    D3D_FEATURE_LEVEL feature_level;
    HRESULT hr = D3D11CreateDevice(
        nullptr,                          // 默认适配器
        D3D_DRIVER_TYPE_HARDWARE,         // 硬件驱动
        nullptr,                          // 软件驱动句柄
        D3D11_CREATE_DEVICE_BGRA_SUPPORT, // 支持BGRA
        feature_levels,
        ARRAYSIZE(feature_levels),
        D3D11_SDK_VERSION,
        d3d11_device_.put(),
        &feature_level,
        d3d11_context_.put());

    if (FAILED(hr))
    {
        std::cerr << "Failed to create D3D11 device, HRESULT: 0x" << std::hex << hr << std::endl;
        return false;
    }

    std::cout << "D3D11 device created successfully" << std::endl;
    return true;
}

bool WinRTCaptureManager::CreateWinRTDevice()
{
    try
    {
        // 获取DXGI设备
        auto dxgi_device = d3d11_device_.as<IDXGIDevice>();

        // 创建WinRT设备
        com_ptr<IInspectable> inspectable;
        HRESULT hr = CreateDirect3D11DeviceFromDXGIDevice(dxgi_device.get(), inspectable.put());

        if (FAILED(hr))
        {
            std::cerr << "Failed to create WinRT device from DXGI device" << std::endl;
            return false;
        }

        winrt_device_ = inspectable.as<IDirect3DDevice>();
        std::cout << "WinRT device created successfully" << std::endl;
        return true;
    }
    catch (...)
    {
        std::cerr << "Exception creating WinRT device" << std::endl;
        return false;
    }
}

bool WinRTCaptureManager::CreateCaptureItem(HWND target_window, HMONITOR monitor_handle)
{
    try
    {
        if (target_window)
        {
            // 捕获指定窗口
            auto interop = get_activation_factory<GraphicsCaptureItem, IGraphicsCaptureItemInterop>();
            HRESULT hr = interop->CreateForWindow(target_window,
                                                  guid_of<ABI::Windows::Graphics::Capture::IGraphicsCaptureItem>(),
                                                  reinterpret_cast<void **>(put_abi(capture_item_)));
            if (FAILED(hr))
            {
                std::cerr << "Failed to create capture item for window" << std::endl;
                return false;
            }
        }
        else
        {
            // 捕获显示器
            if (!monitor_handle)
            {
                monitor_handle = WinRTCaptureHelper::GetPrimaryMonitor();
            }

            auto interop = get_activation_factory<GraphicsCaptureItem, IGraphicsCaptureItemInterop>();
            HRESULT hr = interop->CreateForMonitor(monitor_handle,
                                                   guid_of<ABI::Windows::Graphics::Capture::IGraphicsCaptureItem>(),
                                                   reinterpret_cast<void **>(put_abi(capture_item_)));
            if (FAILED(hr))
            {
                std::cerr << "Failed to create capture item for monitor" << std::endl;
                return false;
            }
        }

        // 获取捕获尺寸
        auto size = capture_item_.Size();
        capture_width_ = size.Width;
        capture_height_ = size.Height;

        std::cout << "Capture item created for size: " << capture_width_ << "x" << capture_height_ << std::endl;
        return true;
    }
    catch (...)
    {
        std::cerr << "Exception creating capture item" << std::endl;
        return false;
    }
}

bool WinRTCaptureManager::CreateFramePool()
{
    try
    {
        // 创建帧池，使用BGRA格式
        frame_pool_ = Direct3D11CaptureFramePool::Create(
            winrt_device_,
            DirectXPixelFormat::B8G8R8A8UIntNormalized,
            2, // 缓冲2帧
            capture_item_.Size());

        if (!frame_pool_)
        {
            std::cerr << "Failed to create frame pool" << std::endl;
            return false;
        }

        std::cout << "Frame pool created successfully" << std::endl;
        return true;
    }
    catch (...)
    {
        std::cerr << "Exception creating frame pool" << std::endl;
        return false;
    }
}

bool WinRTCaptureManager::StartCapture()
{
    try
    {
        // 创建捕获会话
        capture_session_ = frame_pool_.CreateCaptureSession(capture_item_);

        if (!capture_session_)
        {
            std::cerr << "Failed to create capture session" << std::endl;
            return false;
        }

        // 开始捕获
        capture_session_.StartCapture();

        std::cout << "Capture session started successfully" << std::endl;
        return true;
    }
    catch (...)
    {
        std::cerr << "Exception starting capture" << std::endl;
        return false;
    }
}

bool WinRTCaptureManager::CaptureFrame(std::vector<uint8_t> &frame_data, int &width, int &height)
{
    if (!initialized_)
    {
        std::cerr << "WinRT capture manager not initialized" << std::endl;
        return false;
    }

    try
    {
        // 尝试获取最新帧
        auto frame = frame_pool_.TryGetNextFrame();
        if (!frame)
        {
            // 没有新帧可用
            return false;
        }

        // 获取帧的表面
        auto surface = frame.Surface();

        // 获取D3D11纹理
        auto access = surface.as<Windows::Graphics::DirectX::Direct3D11::IDirect3DDxgiInterfaceAccess>();
        com_ptr<ID3D11Texture2D> texture;
        HRESULT hr = access->GetInterface(IID_PPV_ARGS(&texture));

        if (FAILED(hr))
        {
            std::cerr << "Failed to get D3D11 texture from frame" << std::endl;
            return false;
        }

        // 获取纹理描述
        D3D11_TEXTURE2D_DESC desc;
        texture->GetDesc(&desc);

        width = desc.Width;
        height = desc.Height;

        // 使用纹理处理器转换为内存数据
        return texture_processor_->ConvertTextureToMemory(texture.get(), frame_data, width, height);
    }
    catch (...)
    {
        std::cerr << "Exception during frame capture" << std::endl;
        return false;
    }
}

void WinRTCaptureManager::Cleanup()
{
    try
    {
        if (capture_session_)
        {
            capture_session_.Close();
            capture_session_ = nullptr;
        }

        if (frame_pool_)
        {
            frame_pool_.Close();
            frame_pool_ = nullptr;
        }

        capture_item_ = nullptr;
        winrt_device_ = nullptr;

        if (texture_processor_)
        {
            texture_processor_->Cleanup();
        }

        if (buffer_pool_)
        {
            buffer_pool_->Clear();
        }

        d3d11_context_ = nullptr;
        d3d11_device_ = nullptr;

        initialized_ = false;
        std::cout << "WinRT capture manager cleaned up" << std::endl;
    }
    catch (...)
    {
        std::cerr << "Exception during cleanup" << std::endl;
    }
}

// D3D11TextureProcessor 实现
D3D11TextureProcessor::D3D11TextureProcessor()
    : d3d11_device_(nullptr), d3d11_context_(nullptr), current_width_(0), current_height_(0), initialized_(false)
{
}

D3D11TextureProcessor::~D3D11TextureProcessor()
{
    Cleanup();
}

bool D3D11TextureProcessor::Initialize(ID3D11Device *device, ID3D11DeviceContext *context)
{
    if (!device || !context)
    {
        std::cerr << "Invalid D3D11 device or context" << std::endl;
        return false;
    }

    d3d11_device_ = device;
    d3d11_context_ = context;
    initialized_ = true;

    std::cout << "D3D11 texture processor initialized" << std::endl;
    return true;
}

bool D3D11TextureProcessor::ConvertTextureToMemory(ID3D11Texture2D *source_texture,
                                                   std::vector<uint8_t> &output_data,
                                                   int width, int height)
{
    if (!initialized_ || !source_texture)
    {
        return false;
    }

    try
    {
        // 确保staging纹理存在且尺寸正确
        if (!UpdateStagingTexture(width, height))
        {
            std::cerr << "Failed to update staging texture" << std::endl;
            return false;
        }

        // 复制源纹理到staging纹理
        d3d11_context_->CopyResource(staging_texture_.get(), source_texture);

        // 映射staging纹理以读取数据
        D3D11_MAPPED_SUBRESOURCE mapped_resource;
        HRESULT hr = d3d11_context_->Map(staging_texture_.get(), 0, D3D11_MAP_READ, 0, &mapped_resource);

        if (FAILED(hr))
        {
            std::cerr << "Failed to map staging texture, HRESULT: 0x" << std::hex << hr << std::endl;
            return false;
        }

        // 计算数据大小并调整输出缓冲区
        size_t frame_size = width * height * 4; // BGRA格式
        output_data.resize(frame_size);

        // 复制像素数据，考虑行对齐
        uint8_t *src_data = static_cast<uint8_t *>(mapped_resource.pData);
        uint8_t *dst_data = output_data.data();
        size_t row_pitch = mapped_resource.RowPitch;
        size_t row_size = width * 4; // BGRA每像素4字节

        for (int y = 0; y < height; ++y)
        {
            memcpy(dst_data + y * row_size, src_data + y * row_pitch, row_size);
        }

        // 取消映射
        d3d11_context_->Unmap(staging_texture_.get(), 0);

        return true;
    }
    catch (...)
    {
        std::cerr << "Exception in ConvertTextureToMemory" << std::endl;
        return false;
    }
}

bool D3D11TextureProcessor::UpdateStagingTexture(int width, int height)
{
    // 检查是否需要重新创建staging纹理
    if (!staging_texture_ || current_width_ != width || current_height_ != height)
    {
        return CreateStagingTexture(width, height);
    }
    return true;
}

bool D3D11TextureProcessor::CreateStagingTexture(int width, int height)
{
    try
    {
        // 创建staging纹理描述
        D3D11_TEXTURE2D_DESC desc = {};
        desc.Width = width;
        desc.Height = height;
        desc.MipLevels = 1;
        desc.ArraySize = 1;
        desc.Format = DXGI_FORMAT_B8G8R8A8_UNORM; // BGRA格式
        desc.SampleDesc.Count = 1;
        desc.SampleDesc.Quality = 0;
        desc.Usage = D3D11_USAGE_STAGING; // staging纹理用于CPU读取
        desc.BindFlags = 0;
        desc.CPUAccessFlags = D3D11_CPU_ACCESS_READ; // CPU可读
        desc.MiscFlags = 0;

        // 创建staging纹理
        HRESULT hr = d3d11_device_->CreateTexture2D(&desc, nullptr, staging_texture_.put());

        if (FAILED(hr))
        {
            std::cerr << "Failed to create staging texture, HRESULT: 0x" << std::hex << hr << std::endl;
            return false;
        }

        current_width_ = width;
        current_height_ = height;

        std::cout << "Created staging texture: " << width << "x" << height << std::endl;
        return true;
    }
    catch (...)
    {
        std::cerr << "Exception creating staging texture" << std::endl;
        return false;
    }
}

void D3D11TextureProcessor::Cleanup()
{
    staging_texture_ = nullptr;
    d3d11_context_ = nullptr;
    d3d11_device_ = nullptr;
    current_width_ = 0;
    current_height_ = 0;
    initialized_ = false;
}

// FrameBufferPool 实现
FrameBufferPool::FrameBufferPool(size_t pool_size)
    : max_pool_size_(pool_size), current_pool_size_(0)
{
}

FrameBufferPool::~FrameBufferPool()
{
    Clear();
}

std::vector<uint8_t> FrameBufferPool::GetBuffer(size_t size)
{
    std::lock_guard<std::mutex> lock(buffer_mutex_);

    // 尝试从池中获取合适的缓冲区
    if (!available_buffers_.empty())
    {
        auto buffer = std::move(available_buffers_.front());
        available_buffers_.pop();
        current_pool_size_--;

        // 调整缓冲区大小
        if (buffer.size() != size)
        {
            buffer.resize(size);
        }

        return buffer;
    }

    // 池中没有可用缓冲区，创建新的
    std::vector<uint8_t> buffer(size);
    return buffer;
}

void FrameBufferPool::ReturnBuffer(std::vector<uint8_t> &&buffer)
{
    std::lock_guard<std::mutex> lock(buffer_mutex_);

    // 如果池未满，将缓冲区放回池中
    if (current_pool_size_ < max_pool_size_)
    {
        available_buffers_.push(std::move(buffer));
        current_pool_size_++;
    }
    // 否则让缓冲区自动销毁
}

void FrameBufferPool::Clear()
{
    std::lock_guard<std::mutex> lock(buffer_mutex_);

    // 清空所有缓冲区
    while (!available_buffers_.empty())
    {
        available_buffers_.pop();
    }
    current_pool_size_ = 0;
}

// WinRTCaptureHelper 实现
namespace WinRTCaptureHelper
{
    bool CheckWindowsVersion()
    {
        // 检查是否为Windows 10 1903 (Build 18362) 或更高版本
        // WinRT Graphics Capture API 需要此版本
        OSVERSIONINFOEXW osvi = {};
        osvi.dwOSVersionInfoSize = sizeof(osvi);
        osvi.dwMajorVersion = 10;
        osvi.dwMinorVersion = 0;
        osvi.dwBuildNumber = 18362; // Windows 10 1903

        DWORDLONG condition_mask = 0;
        VER_SET_CONDITION(condition_mask, VER_MAJORVERSION, VER_GREATER_EQUAL);
        VER_SET_CONDITION(condition_mask, VER_MINORVERSION, VER_GREATER_EQUAL);
        VER_SET_CONDITION(condition_mask, VER_BUILDNUMBER, VER_GREATER_EQUAL);

        return VerifyVersionInfoW(&osvi,
                                  VER_MAJORVERSION | VER_MINORVERSION | VER_BUILDNUMBER,
                                  condition_mask) != FALSE;
    }

    HMONITOR GetPrimaryMonitor()
    {
        return MonitorFromWindow(nullptr, MONITOR_DEFAULTTOPRIMARY);
    }

    HMONITOR GetMonitorFromPoint(int x, int y)
    {
        POINT point = {x, y};
        return MonitorFromPoint(point, MONITOR_DEFAULTTONEAREST);
    }

    bool GetMonitorSize(HMONITOR monitor, int &width, int &height)
    {
        MONITORINFO monitor_info = {};
        monitor_info.cbSize = sizeof(MONITORINFO);

        if (GetMonitorInfo(monitor, &monitor_info))
        {
            width = monitor_info.rcMonitor.right - monitor_info.rcMonitor.left;
            height = monitor_info.rcMonitor.bottom - monitor_info.rcMonitor.top;
            return true;
        }

        return false;
    }
}
