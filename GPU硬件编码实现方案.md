# GPU硬件编码实现方案

## 概述

为项目添加GPU硬件编码支持，包括NVENC (NVIDIA)、AMF (AMD)、QSV (Intel)三大主流硬件编码器，大幅提升编码性能并降低CPU占用。

## 技术架构

### 1. 硬件编码器支持

#### NVENC (NVIDIA)
- **编码器名称**: `h264_nvenc`, `hevc_nvenc`
- **支持显卡**: GTX 600系列及以上
- **特性**: 高质量、低延迟、多并发流
- **优势**: 成熟稳定，质量接近软件编码

#### AMF (AMD)
- **编码器名称**: `h264_amf`, `hevc_amf`  
- **支持显卡**: GCN架构及以上
- **特性**: 低功耗、高效率
- **优势**: AMD显卡专用，功耗控制好

#### QSV (Intel)
- **编码器名称**: `h264_qsv`, `hevc_qsv`
- **支持处理器**: 4代酷睿及以上
- **特性**: 集成度高、兼容性好
- **优势**: 笔记本和低功耗设备友好

### 2. 智能编码器选择

#### 选择策略
```
1. 检测可用的硬件编码器
2. 按优先级选择: NVENC > AMF > QSV > 软件编码
3. 根据质量要求和性能需求动态调整
4. 提供手动指定选项
```

#### 回退机制
```
硬件编码器1 → 硬件编码器2 → 软件编码 → 错误
```

## 实现设计

### 1. 编码器管理器

```cpp
class HardwareEncoderManager {
public:
    enum EncoderType {
        ENCODER_SOFTWARE,   // libx264
        ENCODER_NVENC,      // h264_nvenc
        ENCODER_AMF,        // h264_amf  
        ENCODER_QSV         // h264_qsv
    };
    
    // 检测可用编码器
    std::vector<EncoderType> DetectAvailableEncoders();
    
    // 选择最佳编码器
    EncoderType SelectBestEncoder(int width, int height, int fps);
    
    // 创建编码器上下文
    AVCodecContext* CreateEncoderContext(EncoderType type, int width, int height, int fps);
};
```

### 2. 编码器配置

#### NVENC配置
```cpp
bool ConfigureNVENC(AVCodecContext* ctx, int width, int height, int fps) {
    // 基础参数
    ctx->codec_id = AV_CODEC_ID_H264;
    ctx->width = width;
    ctx->height = height;
    ctx->time_base = {1, fps};
    ctx->framerate = {fps, 1};
    ctx->pix_fmt = AV_PIX_FMT_NV12; // NVENC偏好格式
    
    // NVENC特定参数
    av_opt_set(ctx->priv_data, "preset", "p4", 0);        // 平衡质量和速度
    av_opt_set(ctx->priv_data, "tune", "ll", 0);          // 低延迟
    av_opt_set(ctx->priv_data, "rc", "cbr", 0);           // 恒定码率
    av_opt_set(ctx->priv_data, "profile", "high", 0);     // High Profile
    av_opt_set_int(ctx->priv_data, "zerolatency", 1, 0);  // 零延迟模式
    av_opt_set_int(ctx->priv_data, "delay", 0, 0);        // 无延迟
    
    return true;
}
```

#### AMF配置
```cpp
bool ConfigureAMF(AVCodecContext* ctx, int width, int height, int fps) {
    ctx->codec_id = AV_CODEC_ID_H264;
    ctx->width = width;
    ctx->height = height;
    ctx->time_base = {1, fps};
    ctx->framerate = {fps, 1};
    ctx->pix_fmt = AV_PIX_FMT_NV12;
    
    // AMF特定参数
    av_opt_set(ctx->priv_data, "usage", "lowlatency", 0);
    av_opt_set(ctx->priv_data, "profile", "high", 0);
    av_opt_set(ctx->priv_data, "quality", "balanced", 0);
    av_opt_set_int(ctx->priv_data, "enforce_hrd", 1, 0);
    
    return true;
}
```

#### QSV配置
```cpp
bool ConfigureQSV(AVCodecContext* ctx, int width, int height, int fps) {
    ctx->codec_id = AV_CODEC_ID_H264;
    ctx->width = width;
    ctx->height = height;
    ctx->time_base = {1, fps};
    ctx->framerate = {fps, 1};
    ctx->pix_fmt = AV_PIX_FMT_NV12;
    
    // QSV特定参数
    av_opt_set(ctx->priv_data, "preset", "medium", 0);
    av_opt_set(ctx->priv_data, "profile", "high", 0);
    av_opt_set_int(ctx->priv_data, "async_depth", 1, 0);  // 低延迟
    av_opt_set_int(ctx->priv_data, "look_ahead", 0, 0);   // 禁用前瞻
    
    return true;
}
```

### 3. 像素格式转换优化

#### GPU加速转换
```cpp
class PixelFormatConverter {
private:
    struct SwsContext* bgra_to_nv12_ctx_;
    struct SwsContext* bgra_to_yuv420p_ctx_;
    
public:
    // 针对硬件编码器优化的转换
    bool ConvertBGRAToNV12(const std::vector<uint8_t>& bgra_data, 
                          AVFrame* nv12_frame, int width, int height);
    
    // 软件编码器使用的转换
    bool ConvertBGRAToYUV420P(const std::vector<uint8_t>& bgra_data,
                             AVFrame* yuv420p_frame, int width, int height);
};
```

### 4. 性能监控

#### 编码性能指标
```cpp
struct EncodingMetrics {
    EncoderType current_encoder;
    double avg_encoding_time_ms;
    double max_encoding_time_ms;
    int successful_frames;
    int failed_frames;
    double cpu_usage_percent;
    double gpu_usage_percent;
    size_t gpu_memory_used_mb;
};
```

## 集成到现有代码

### 1. 修改StreamPusher类

```cpp
class StreamPusher {
private:
    std::unique_ptr<HardwareEncoderManager> encoder_manager_;
    std::unique_ptr<PixelFormatConverter> pixel_converter_;
    HardwareEncoderManager::EncoderType current_encoder_type_;
    EncodingMetrics metrics_;
    
public:
    // 新增方法
    bool SetPreferredEncoder(HardwareEncoderManager::EncoderType type);
    EncodingMetrics GetEncodingMetrics() const;
    bool SwitchEncoder(HardwareEncoderManager::EncoderType new_type);
};
```

### 2. 智能编码器选择

```cpp
bool StreamPusher::InitializeCodec(int width, int height, int fps) {
    // 检测可用编码器
    auto available_encoders = encoder_manager_->DetectAvailableEncoders();
    
    if (available_encoders.empty()) {
        std::cerr << "No encoders available" << std::endl;
        return false;
    }
    
    // 选择最佳编码器
    current_encoder_type_ = encoder_manager_->SelectBestEncoder(width, height, fps);
    
    // 创建编码器上下文
    codec_context_ = encoder_manager_->CreateEncoderContext(
        current_encoder_type_, width, height, fps);
    
    if (!codec_context_) {
        std::cerr << "Failed to create encoder context" << std::endl;
        return false;
    }
    
    // 打开编码器
    int ret = avcodec_open2(codec_context_, codec_context_->codec, nullptr);
    if (ret < 0) {
        // 硬件编码器失败，尝试软件编码器
        return FallbackToSoftwareEncoder(width, height, fps);
    }
    
    std::cout << "✅ Hardware encoder initialized: " 
              << GetEncoderName(current_encoder_type_) << std::endl;
    return true;
}
```

### 3. 动态编码器切换

```cpp
bool StreamPusher::SwitchEncoder(HardwareEncoderManager::EncoderType new_type) {
    // 保存当前状态
    int64_t current_pts = frame_count_;
    
    // 刷新当前编码器
    FlushEncoder();
    
    // 关闭当前编码器
    avcodec_free_context(&codec_context_);
    
    // 创建新编码器
    codec_context_ = encoder_manager_->CreateEncoderContext(
        new_type, frame_width_, frame_height_, frame_rate_);
    
    if (!codec_context_) {
        return false;
    }
    
    // 打开新编码器
    int ret = avcodec_open2(codec_context_, codec_context_->codec, nullptr);
    if (ret < 0) {
        return false;
    }
    
    // 恢复状态
    frame_count_ = current_pts;
    current_encoder_type_ = new_type;
    
    std::cout << "Switched to encoder: " << GetEncoderName(new_type) << std::endl;
    return true;
}
```

## 预期性能提升

### 编码性能对比
| 编码器 | CPU占用 | 编码速度 | 质量 | 延迟 | 功耗 |
|--------|---------|----------|------|------|------|
| libx264 | 100% | 1x | 最高 | 中等 | 高 |
| NVENC | 5-10% | 3-5x | 高 | 最低 | 中等 |
| AMF | 5-10% | 2-4x | 高 | 低 | 最低 |
| QSV | 10-15% | 2-3x | 中高 | 低 | 低 |

### 系统资源释放
- **CPU占用**: 降低80-95%
- **系统温度**: 降低20-40%
- **功耗**: 降低30-60%
- **并发能力**: 提升3-5倍

## 实施计划

### 阶段1: 基础框架 (3天)
- [ ] 实现HardwareEncoderManager类
- [ ] 添加编码器检测功能
- [ ] 实现基础的NVENC支持

### 阶段2: 多编码器支持 (3天)
- [ ] 添加AMF编码器支持
- [ ] 添加QSV编码器支持
- [ ] 实现智能选择算法

### 阶段3: 优化和集成 (2天)
- [ ] 优化像素格式转换
- [ ] 集成到现有StreamPusher
- [ ] 添加性能监控

### 阶段4: 测试和调优 (2天)
- [ ] 全面兼容性测试
- [ ] 性能基准测试
- [ ] 稳定性测试

这个实现将显著提升编码性能，特别是在高分辨率和高帧率场景下，同时保持良好的兼容性和稳定性。
