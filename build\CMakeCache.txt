# This is the CMakeCache file.
# For build in directory: c:/dev/nuaa/push_video_stream_server/ffmpeg_push/build
# It was generated by CMake: C:/Program Files/CMake/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Path to a program.
CMAKE_AR:FILEPATH=C:/Program Files/Microsoft Visual Studio/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lib.exe

//No help, variable specified on the command line.
CMAKE_BUILD_TYPE:UNINITIALIZED=Release

//Semicolon separated list of supported configuration types, only
// supports Debug, Release, MinSizeRel, and RelWithDebInfo, anything
// else will be ignored.
CMAKE_CONFIGURATION_TYPES:STRING=Debug;Release;MinSizeRel;RelWithDebInfo

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=/utf-8

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=/Zi /Ob0 /Od /RTC1

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=/O1 /Ob1 /DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=/O2 /Ob2 /DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=/Zi /O2 /Ob1 /DNDEBUG

//No help, variable specified on the command line.
CMAKE_CXX_STANDARD:UNINITIALIZED=20

//Libraries linked by default with all C++ applications.
CMAKE_CXX_STANDARD_LIBRARIES:STRING=kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=/DWIN32 /D_WINDOWS

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=/Zi /Ob0 /Od /RTC1

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=/O1 /Ob1 /DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=/O2 /Ob2 /DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=/Zi /O2 /Ob1 /DNDEBUG

//Libraries linked by default with all C applications.
CMAKE_C_STANDARD_LIBRARIES:STRING=kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=C:/dev/nuaa/push_video_stream_server/ffmpeg_push/build/CMakeFiles/pkgRedirects

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=C:/Program Files/screen_capture_stream

//Path to a program.
CMAKE_LINKER:FILEPATH=C:/Program Files/Microsoft Visual Studio/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.exe

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//Path to a program.
CMAKE_MT:FILEPATH=CMAKE_MT-NOTFOUND

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=screen_capture_stream

//RC compiler
CMAKE_RC_COMPILER:FILEPATH=C:/Program Files (x86)/Embarcadero/Studio/21.0/bin/rc.exe

//Flags for Windows Resource Compiler during all build types.
CMAKE_RC_FLAGS:STRING=-DWIN32

//Flags for Windows Resource Compiler during DEBUG builds.
CMAKE_RC_FLAGS_DEBUG:STRING=-D_DEBUG

//Flags for Windows Resource Compiler during MINSIZEREL builds.
CMAKE_RC_FLAGS_MINSIZEREL:STRING=

//Flags for Windows Resource Compiler during RELEASE builds.
CMAKE_RC_FLAGS_RELEASE:STRING=

//Flags for Windows Resource Compiler during RELWITHDEBINFO builds.
CMAKE_RC_FLAGS_RELWITHDEBINFO:STRING=

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//No help, variable specified on the command line.
CMAKE_TOOLCHAIN_FILE:UNINITIALIZED=C:\dev\vcpkg\scripts\buildsystems\vcpkg.cmake

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//The directory containing a CMake configuration file for OpenCV.
OpenCV_DIR:PATH=C:/dev/vcpkg/installed/x64-windows/share/opencv4

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=C:/dev/vcpkg/installed/x64-windows/tools/pkgconf/pkgconf.exe

//The directory containing a CMake configuration file for Protobuf.
Protobuf_DIR:PATH=C:/dev/vcpkg/installed/x64-windows/share/protobuf

//Path to a program.
Protobuf_PROTOC_EXECUTABLE:FILEPATH=C:/dev/vcpkg/installed/x64-windows/tools/protobuf/protoc.exe

//Path to a file.
TIFF_INCLUDE_DIR:PATH=C:/dev/vcpkg/installed/x64-windows/include

//Path to a library.
TIFF_LIBRARY_DEBUG:FILEPATH=C:/dev/vcpkg/installed/x64-windows/debug/lib/tiffd.lib

//Path to a library.
TIFF_LIBRARY_RELEASE:FILEPATH=C:/dev/vcpkg/installed/x64-windows/lib/tiff.lib

//Automatically copy dependencies into the output directory for
// executables.
VCPKG_APPLOCAL_DEPS:BOOL=ON

//The directory which contains the installed libraries for each
// triplet
VCPKG_INSTALLED_DIR:PATH=C:/dev/vcpkg/installed

//The path to the vcpkg manifest directory.
VCPKG_MANIFEST_DIR:PATH=

//Use manifest mode, as opposed to classic mode.
VCPKG_MANIFEST_MODE:BOOL=OFF

//Appends the vcpkg paths to CMAKE_PREFIX_PATH, CMAKE_LIBRARY_PATH
// and CMAKE_FIND_ROOT_PATH so that vcpkg libraries/packages are
// found after toolchain/system libraries/packages.
VCPKG_PREFER_SYSTEM_LIBS:BOOL=OFF

//Enable the setup of CMAKE_PROGRAM_PATH to vcpkg paths
VCPKG_SETUP_CMAKE_PROGRAM_PATH:BOOL=ON

//Vcpkg target triplet (ex. x86-windows)
VCPKG_TARGET_TRIPLET:STRING=x64-windows

//Trace calls to find_package()
VCPKG_TRACE_FIND_PACKAGE:BOOL=OFF

//Enables messages from the VCPKG toolchain for debugging purposes.
VCPKG_VERBOSE:BOOL=OFF

//(experimental) Automatically copy dependencies into the install
// target directory for executables. Requires CMake 3.14.
X_VCPKG_APPLOCAL_DEPS_INSTALL:BOOL=OFF

//(experimental) Add USES_TERMINAL to VCPKG_APPLOCAL_DEPS to force
// serialization.
X_VCPKG_APPLOCAL_DEPS_SERIALIZED:BOOL=OFF

//Path to a program.
Z_VCPKG_PWSH_PATH:FILEPATH=C:/Program Files/PowerShell/7/pwsh.exe

//The directory which contains the installed libraries for each
// triplet
_VCPKG_INSTALLED_DIR:PATH=C:/dev/vcpkg/installed

//The directory containing a CMake configuration file for absl.
absl_DIR:PATH=C:/dev/vcpkg/installed/x64-windows/share/absl

//Path to a library.
pkgcfg_lib_FFMPEG_avcodec:FILEPATH=C:/dev/vcpkg/installed/x64-windows/debug/lib/avcodec.lib

//Path to a library.
pkgcfg_lib_FFMPEG_avdevice:FILEPATH=C:/dev/vcpkg/installed/x64-windows/debug/lib/avdevice.lib

//Path to a library.
pkgcfg_lib_FFMPEG_avformat:FILEPATH=C:/dev/vcpkg/installed/x64-windows/debug/lib/avformat.lib

//Path to a library.
pkgcfg_lib_FFMPEG_avutil:FILEPATH=C:/dev/vcpkg/installed/x64-windows/debug/lib/avutil.lib

//Path to a library.
pkgcfg_lib_FFMPEG_bcrypt:FILEPATH=pkgcfg_lib_FFMPEG_bcrypt-NOTFOUND

//Path to a library.
pkgcfg_lib_FFMPEG_gdi32:FILEPATH=pkgcfg_lib_FFMPEG_gdi32-NOTFOUND

//Path to a library.
pkgcfg_lib_FFMPEG_mfuuid:FILEPATH=pkgcfg_lib_FFMPEG_mfuuid-NOTFOUND

//Path to a library.
pkgcfg_lib_FFMPEG_ole32:FILEPATH=pkgcfg_lib_FFMPEG_ole32-NOTFOUND

//Path to a library.
pkgcfg_lib_FFMPEG_oleaut32:FILEPATH=pkgcfg_lib_FFMPEG_oleaut32-NOTFOUND

//Path to a library.
pkgcfg_lib_FFMPEG_psapi:FILEPATH=pkgcfg_lib_FFMPEG_psapi-NOTFOUND

//Path to a library.
pkgcfg_lib_FFMPEG_secur32:FILEPATH=pkgcfg_lib_FFMPEG_secur32-NOTFOUND

//Path to a library.
pkgcfg_lib_FFMPEG_shlwapi:FILEPATH=pkgcfg_lib_FFMPEG_shlwapi-NOTFOUND

//Path to a library.
pkgcfg_lib_FFMPEG_strmiids:FILEPATH=pkgcfg_lib_FFMPEG_strmiids-NOTFOUND

//Path to a library.
pkgcfg_lib_FFMPEG_swresample:FILEPATH=C:/dev/vcpkg/installed/x64-windows/debug/lib/swresample.lib

//Path to a library.
pkgcfg_lib_FFMPEG_swscale:FILEPATH=C:/dev/vcpkg/installed/x64-windows/debug/lib/swscale.lib

//Path to a library.
pkgcfg_lib_FFMPEG_user32:FILEPATH=pkgcfg_lib_FFMPEG_user32-NOTFOUND

//Path to a library.
pkgcfg_lib_FFMPEG_uuid:FILEPATH=pkgcfg_lib_FFMPEG_uuid-NOTFOUND

//Path to a library.
pkgcfg_lib_FFMPEG_vfw32:FILEPATH=pkgcfg_lib_FFMPEG_vfw32-NOTFOUND

//Path to a library.
pkgcfg_lib_FFMPEG_ws2_32:FILEPATH=pkgcfg_lib_FFMPEG_ws2_32-NOTFOUND

//CMake built-in FindProtobuf.cmake module compatible
protobuf_MODULE_COMPATIBLE:BOOL=OFF

//Enable for verbose output
protobuf_VERBOSE:BOOL=OFF

//The directory containing a CMake configuration file for quirc.
quirc_DIR:PATH=C:/dev/vcpkg/installed/x64-windows/share/quirc

//Value Computed by CMake
screen_capture_stream_BINARY_DIR:STATIC=C:/dev/nuaa/push_video_stream_server/ffmpeg_push/build

//Value Computed by CMake
screen_capture_stream_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
screen_capture_stream_SOURCE_DIR:STATIC=C:/dev/nuaa/push_video_stream_server/ffmpeg_push

//The directory containing a CMake configuration file for utf8_range.
utf8_range_DIR:PATH=C:/dev/vcpkg/installed/x64-windows/share/utf8_range


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=c:/dev/nuaa/push_video_stream_server/ffmpeg_push/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=26
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=4
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cpack.exe
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=C:/Program Files/CMake/bin/ctest.exe
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_STANDARD_LIBRARIES
CMAKE_CXX_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_STANDARD_LIBRARIES
CMAKE_C_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=Unknown
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Visual Studio 17 2022
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=C:/Program Files/Microsoft Visual Studio/2022/Enterprise
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=x64
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=
//Have library pthreads
CMAKE_HAVE_PTHREADS_CREATE:INTERNAL=
//Have library pthread
CMAKE_HAVE_PTHREAD_CREATE:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=C:/dev/nuaa/push_video_stream_server/ffmpeg_push
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MT
CMAKE_MT-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//noop for ranlib
CMAKE_RANLIB:INTERNAL=:
//ADVANCED property for variable: CMAKE_RC_COMPILER
CMAKE_RC_COMPILER-ADVANCED:INTERNAL=1
CMAKE_RC_COMPILER_WORKS:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS
CMAKE_RC_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_DEBUG
CMAKE_RC_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_MINSIZEREL
CMAKE_RC_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELEASE
CMAKE_RC_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELWITHDEBINFO
CMAKE_RC_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=C:/Program Files/CMake/share/cmake-3.26
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TOOLCHAIN_FILE
CMAKE_TOOLCHAIN_FILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
FFMPEG_CFLAGS:INTERNAL=-IC:/dev/vcpkg/installed/x64-windows/debug/../include
FFMPEG_CFLAGS_I:INTERNAL=
FFMPEG_CFLAGS_OTHER:INTERNAL=
FFMPEG_FOUND:INTERNAL=1
FFMPEG_INCLUDEDIR:INTERNAL=
FFMPEG_INCLUDE_DIRS:INTERNAL=C:/dev/vcpkg/installed/x64-windows/debug/../include
FFMPEG_LDFLAGS:INTERNAL=-LC:/dev/vcpkg/installed/x64-windows/debug/lib;-lavformat;-lsecur32;-lws2_32;-lavcodec;-lmfuuid;-lole32;-lstrmiids;-lole32;-luser32;-lswscale;-lswresample;-lavutil;-luser32;-lbcrypt
FFMPEG_LDFLAGS_OTHER:INTERNAL=
FFMPEG_LIBDIR:INTERNAL=
FFMPEG_LIBRARIES:INTERNAL=avformat;secur32;ws2_32;avcodec;mfuuid;ole32;strmiids;ole32;user32;swscale;swresample;avutil;user32;bcrypt
FFMPEG_LIBRARY_DIRS:INTERNAL=C:/dev/vcpkg/installed/x64-windows/debug/lib
FFMPEG_LIBS:INTERNAL=
FFMPEG_LIBS_L:INTERNAL=
FFMPEG_LIBS_OTHER:INTERNAL=
FFMPEG_LIBS_PATHS:INTERNAL=
FFMPEG_MODULE_NAME:INTERNAL=
FFMPEG_PREFIX:INTERNAL=
FFMPEG_STATIC_CFLAGS:INTERNAL=-IC:/dev/vcpkg/installed/x64-windows/debug/../include
FFMPEG_STATIC_CFLAGS_I:INTERNAL=
FFMPEG_STATIC_CFLAGS_OTHER:INTERNAL=
FFMPEG_STATIC_INCLUDE_DIRS:INTERNAL=C:/dev/vcpkg/installed/x64-windows/debug/../include
FFMPEG_STATIC_LDFLAGS:INTERNAL=-LC:/dev/vcpkg/installed/x64-windows/debug/lib;-lavformat;-lsecur32;-lws2_32;-lavcodec;-lmfuuid;-lole32;-lstrmiids;-lole32;-luser32;-lswscale;-lswresample;-lavutil;-luser32;-lbcrypt
FFMPEG_STATIC_LDFLAGS_OTHER:INTERNAL=
FFMPEG_STATIC_LIBDIR:INTERNAL=
FFMPEG_STATIC_LIBRARIES:INTERNAL=avformat;secur32;ws2_32;avcodec;mfuuid;ole32;strmiids;ole32;user32;swscale;swresample;avutil;user32;bcrypt
FFMPEG_STATIC_LIBRARY_DIRS:INTERNAL=C:/dev/vcpkg/installed/x64-windows/debug/lib
FFMPEG_STATIC_LIBS:INTERNAL=
FFMPEG_STATIC_LIBS_L:INTERNAL=
FFMPEG_STATIC_LIBS_OTHER:INTERNAL=
FFMPEG_STATIC_LIBS_PATHS:INTERNAL=
FFMPEG_VERSION:INTERNAL=
FFMPEG_libavcodec_INCLUDEDIR:INTERNAL=C:/dev/vcpkg/installed/x64-windows/debug/../include
FFMPEG_libavcodec_LIBDIR:INTERNAL=C:/dev/vcpkg/installed/x64-windows/debug/lib
FFMPEG_libavcodec_MODULE_NAME:INTERNAL=libavcodec
FFMPEG_libavcodec_PREFIX:INTERNAL=C:/dev/vcpkg/installed/x64-windows/debug
FFMPEG_libavcodec_VERSION:INTERNAL=61.19.101
FFMPEG_libavdevice_INCLUDEDIR:INTERNAL=C:/dev/vcpkg/installed/x64-windows/debug/../include
FFMPEG_libavdevice_LIBDIR:INTERNAL=C:/dev/vcpkg/installed/x64-windows/debug/lib
FFMPEG_libavdevice_MODULE_NAME:INTERNAL=libavdevice
FFMPEG_libavdevice_PREFIX:INTERNAL=C:/dev/vcpkg/installed/x64-windows/debug
FFMPEG_libavdevice_VERSION:INTERNAL=61.3.100
FFMPEG_libavformat_INCLUDEDIR:INTERNAL=C:/dev/vcpkg/installed/x64-windows/debug/../include
FFMPEG_libavformat_LIBDIR:INTERNAL=C:/dev/vcpkg/installed/x64-windows/debug/lib
FFMPEG_libavformat_MODULE_NAME:INTERNAL=libavformat
FFMPEG_libavformat_PREFIX:INTERNAL=C:/dev/vcpkg/installed/x64-windows/debug
FFMPEG_libavformat_VERSION:INTERNAL=61.7.100
FFMPEG_libavutil_INCLUDEDIR:INTERNAL=C:/dev/vcpkg/installed/x64-windows/debug/../include
FFMPEG_libavutil_LIBDIR:INTERNAL=C:/dev/vcpkg/installed/x64-windows/debug/lib
FFMPEG_libavutil_MODULE_NAME:INTERNAL=libavutil
FFMPEG_libavutil_PREFIX:INTERNAL=C:/dev/vcpkg/installed/x64-windows/debug
FFMPEG_libavutil_VERSION:INTERNAL=59.39.100
FFMPEG_libswresample_INCLUDEDIR:INTERNAL=C:/dev/vcpkg/installed/x64-windows/debug/../include
FFMPEG_libswresample_LIBDIR:INTERNAL=C:/dev/vcpkg/installed/x64-windows/debug/lib
FFMPEG_libswresample_MODULE_NAME:INTERNAL=libswresample
FFMPEG_libswresample_PREFIX:INTERNAL=C:/dev/vcpkg/installed/x64-windows/debug
FFMPEG_libswresample_VERSION:INTERNAL=5.3.100
FFMPEG_libswscale_INCLUDEDIR:INTERNAL=C:/dev/vcpkg/installed/x64-windows/debug/../include
FFMPEG_libswscale_LIBDIR:INTERNAL=C:/dev/vcpkg/installed/x64-windows/debug/lib
FFMPEG_libswscale_MODULE_NAME:INTERNAL=libswscale
FFMPEG_libswscale_PREFIX:INTERNAL=C:/dev/vcpkg/installed/x64-windows/debug
FFMPEG_libswscale_VERSION:INTERNAL=8.3.100
//Details about finding OpenCV
FIND_PACKAGE_MESSAGE_DETAILS_OpenCV:INTERNAL=[C:/dev/vcpkg/installed/x64-windows][v4.11.0()]
//Details about finding PkgConfig
FIND_PACKAGE_MESSAGE_DETAILS_PkgConfig:INTERNAL=[C:/dev/vcpkg/installed/x64-windows/tools/pkgconf/pkgconf.exe][v2.4.3()]
//Details about finding Protobuf
FIND_PACKAGE_MESSAGE_DETAILS_Protobuf:INTERNAL=[C:/dev/vcpkg/installed/x64-windows/tools/protobuf/protoc.exe][optimized;C:/dev/vcpkg/installed/x64-windows/bin/libprotobuf.dll;debug;C:/dev/vcpkg/installed/x64-windows/debug/bin/libprotobufd.dll][C:/dev/vcpkg/installed/x64-windows/include][v29.3.0()]
//Details about finding TIFF
FIND_PACKAGE_MESSAGE_DETAILS_TIFF:INTERNAL=[optimized;C:/dev/vcpkg/installed/x64-windows/lib/tiff.lib;debug;C:/dev/vcpkg/installed/x64-windows/debug/lib/tiffd.lib][C:/dev/vcpkg/installed/x64-windows/include][c ][v4.7.0()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: TIFF_INCLUDE_DIR
TIFF_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: TIFF_LIBRARY_DEBUG
TIFF_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: TIFF_LIBRARY_RELEASE
TIFF_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//Install the dependencies listed in your manifest:
//\n    If this is off, you will have to manually install your dependencies.
//\n    See https://github.com/microsoft/vcpkg/tree/master/docs/specifications/manifests.md
// for more info.
//\n
VCPKG_MANIFEST_INSTALL:INTERNAL=OFF
//ADVANCED property for variable: VCPKG_VERBOSE
VCPKG_VERBOSE-ADVANCED:INTERNAL=1
//Making sure VCPKG_MANIFEST_MODE doesn't change
Z_VCPKG_CHECK_MANIFEST_MODE:INTERNAL=OFF
//The path to the PowerShell implementation to use.
Z_VCPKG_POWERSHELL_PATH:INTERNAL=C:/Program Files/PowerShell/7/pwsh.exe
//Vcpkg root directory
Z_VCPKG_ROOT_DIR:INTERNAL=C:/dev/vcpkg
__pkg_config_arguments_FFMPEG:INTERNAL=REQUIRED;libavcodec;libavformat;libavutil;libswscale;libswresample
__pkg_config_checked_FFMPEG:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_FFMPEG_avcodec
pkgcfg_lib_FFMPEG_avcodec-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_FFMPEG_avdevice
pkgcfg_lib_FFMPEG_avdevice-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_FFMPEG_avformat
pkgcfg_lib_FFMPEG_avformat-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_FFMPEG_avutil
pkgcfg_lib_FFMPEG_avutil-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_FFMPEG_bcrypt
pkgcfg_lib_FFMPEG_bcrypt-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_FFMPEG_gdi32
pkgcfg_lib_FFMPEG_gdi32-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_FFMPEG_mfuuid
pkgcfg_lib_FFMPEG_mfuuid-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_FFMPEG_ole32
pkgcfg_lib_FFMPEG_ole32-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_FFMPEG_oleaut32
pkgcfg_lib_FFMPEG_oleaut32-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_FFMPEG_psapi
pkgcfg_lib_FFMPEG_psapi-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_FFMPEG_secur32
pkgcfg_lib_FFMPEG_secur32-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_FFMPEG_shlwapi
pkgcfg_lib_FFMPEG_shlwapi-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_FFMPEG_strmiids
pkgcfg_lib_FFMPEG_strmiids-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_FFMPEG_swresample
pkgcfg_lib_FFMPEG_swresample-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_FFMPEG_swscale
pkgcfg_lib_FFMPEG_swscale-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_FFMPEG_user32
pkgcfg_lib_FFMPEG_user32-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_FFMPEG_uuid
pkgcfg_lib_FFMPEG_uuid-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_FFMPEG_vfw32
pkgcfg_lib_FFMPEG_vfw32-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_FFMPEG_ws2_32
pkgcfg_lib_FFMPEG_ws2_32-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=C:/dev/vcpkg/installed/x64-windows/debug/lib
//ADVANCED property for variable: protobuf_MODULE_COMPATIBLE
protobuf_MODULE_COMPATIBLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: protobuf_VERBOSE
protobuf_VERBOSE-ADVANCED:INTERNAL=1

