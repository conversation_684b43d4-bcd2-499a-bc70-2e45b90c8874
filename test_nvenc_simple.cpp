#include <iostream>
#include <vector>
#include <string>

extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/opt.h>
}

int main() {
    std::cout << "Testing NVENC with correct parameters..." << std::endl;
    
    // 查找NVENC编码器
    const AVCodec* codec = avcodec_find_encoder_by_name("h264_nvenc");
    if (!codec) {
        std::cout << "❌ h264_nvenc not found" << std::endl;
        return 1;
    }
    
    std::cout << "✅ Found h264_nvenc codec" << std::endl;
    
    // 创建编码器上下文
    AVCodecContext* ctx = avcodec_alloc_context3(codec);
    if (!ctx) {
        std::cout << "❌ Failed to allocate context" << std::endl;
        return 1;
    }
    
    // 设置基本参数
    ctx->width = 1024;
    ctx->height = 768;
    ctx->time_base = {1, 30};
    ctx->framerate = {30, 1};
    ctx->bit_rate = 2000000;
    ctx->gop_size = 60;
    ctx->max_b_frames = 0;
    ctx->pix_fmt = AV_PIX_FMT_NV12;  // NVENC首选格式
    ctx->profile = FF_PROFILE_H264_HIGH;
    ctx->level = 41;
    
    // 设置NVENC选项
    AVDictionary* opts = nullptr;
    av_dict_set(&opts, "preset", "medium", 0);     // 使用标准preset
    av_dict_set(&opts, "rc", "cbr", 0);            // 恒定码率
    av_dict_set(&opts, "profile", "high", 0);      // High Profile
    av_dict_set(&opts, "level", "4.1", 0);         // Level 4.1
    av_dict_set(&opts, "cq", "20", 0);             // 恒定质量
    av_dict_set(&opts, "delay", "0", 0);           // 无延迟
    av_dict_set(&opts, "gpu", "0", 0);             // 使用第一个GPU
    
    std::cout << "Attempting to open NVENC encoder..." << std::endl;
    
    // 尝试打开编码器
    int ret = avcodec_open2(ctx, codec, &opts);
    if (ret >= 0) {
        std::cout << "🎉 NVENC encoder opened successfully!" << std::endl;
        std::cout << "  Codec: " << codec->name << std::endl;
        std::cout << "  Resolution: " << ctx->width << "x" << ctx->height << std::endl;
        std::cout << "  Pixel format: " << av_get_pix_fmt_name(ctx->pix_fmt) << std::endl;
        std::cout << "  Bitrate: " << ctx->bit_rate << std::endl;
        
        avcodec_close(ctx);
    } else {
        char error_buf[256];
        av_strerror(ret, error_buf, sizeof(error_buf));
        std::cout << "❌ Failed to open NVENC encoder: " << error_buf << std::endl;
        
        // 打印剩余的选项
        AVDictionaryEntry* entry = nullptr;
        std::cout << "Unused options:" << std::endl;
        while ((entry = av_dict_get(opts, "", entry, AV_DICT_IGNORE_SUFFIX))) {
            std::cout << "  " << entry->key << " = " << entry->value << std::endl;
        }
    }
    
    av_dict_free(&opts);
    avcodec_free_context(&ctx);
    
    return ret >= 0 ? 0 : 1;
}
