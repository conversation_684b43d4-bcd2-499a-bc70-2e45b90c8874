#include <iostream>
#include <vector>
#include <string>

extern "C" {
#include <libavcodec/avcodec.h>
}

int main() {
    std::cout << "Testing NVENC support in FFmpeg..." << std::endl;
    
    // 检查多个NVENC编码器变体
    std::vector<std::string> nvenc_codecs = {
        "h264_nvenc",
        "nvenc_h264", 
        "nvenc",
        "hevc_nvenc"
    };
    
    bool found_nvenc = false;
    
    for (const auto& codec_name : nvenc_codecs) {
        const AVCodec* codec = avcodec_find_encoder_by_name(codec_name.c_str());
        if (codec) {
            std::cout << "✅ Found NVENC codec: " << codec_name << std::endl;
            std::cout << "   Long name: " << (codec->long_name ? codec->long_name : "N/A") << std::endl;
            found_nvenc = true;
        } else {
            std::cout << "❌ NVENC codec not found: " << codec_name << std::endl;
        }
    }
    
    if (found_nvenc) {
        std::cout << "\n🎉 NVENC support is available!" << std::endl;
        
        // 尝试创建一个简单的NVENC编码器上下文
        const AVCodec* h264_nvenc = avcodec_find_encoder_by_name("h264_nvenc");
        if (h264_nvenc) {
            AVCodecContext* ctx = avcodec_alloc_context3(h264_nvenc);
            if (ctx) {
                ctx->width = 1920;
                ctx->height = 1080;
                ctx->time_base = {1, 30};
                ctx->framerate = {30, 1};
                ctx->bit_rate = 5000000;
                ctx->pix_fmt = AV_PIX_FMT_NV12;
                
                int ret = avcodec_open2(ctx, h264_nvenc, nullptr);
                if (ret >= 0) {
                    std::cout << "✅ NVENC encoder test successful!" << std::endl;
                    avcodec_close(ctx);
                } else {
                    char error_buf[256];
                    av_strerror(ret, error_buf, sizeof(error_buf));
                    std::cout << "❌ NVENC encoder test failed: " << error_buf << std::endl;
                }
                avcodec_free_context(&ctx);
            }
        }
    } else {
        std::cout << "\n❌ No NVENC support found in this FFmpeg build" << std::endl;
    }
    
    return 0;
}
